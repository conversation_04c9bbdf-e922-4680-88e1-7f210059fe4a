"""
改进的信用卡欺诈检测模型
结合多种技术提高性能
"""

import numpy as np
import pandas as pd
import time
import os
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, 
    roc_auc_score, confusion_matrix, classification_report
)
from imblearn.over_sampling import SMOTE, ADASYN
from imblearn.combine import SMOTEENN, SMOTETomek
from sklearn.feature_selection import SelectFromModel

# 设置环境变量，避免joblib警告
os.environ["LOKY_MAX_CPU_COUNT"] = "4"


def load_creditcard_dataset():
    """
    加载信用卡欺诈检测数据集
    
    返回:
        X (np.ndarray): 特征矩阵
        y (np.ndarray): 标签向量
    """
    print("加载信用卡欺诈检测数据集...")
    try:
        data = pd.read_csv('data/creditcard.csv')
        
        # 创建新特征
        # 时间特征
        data['hour'] = data['Time'].apply(lambda x: (x // 3600) % 24)
        data['day_part'] = data['hour'].apply(lambda x: 0 if 0 <= x < 6 else (1 if 6 <= x < 12 else (2 if 12 <= x < 18 else 3)))
        
        # 金额特征
        data['log_amount'] = np.log(data['Amount'] + 1)
        
        # 交互特征
        data['V1_V2'] = data['V1'] * data['V2']
        data['V1_V3'] = data['V1'] * data['V3']
        data['V2_V3'] = data['V2'] * data['V3']
        
        # 分离特征和标签
        X = data.drop(['Class', 'Time', 'Amount'], axis=1).values
        y = data['Class'].values
        
        # 由于信用卡数据集很大，可以考虑只使用一部分数据
        # 保持所有欺诈样本，随机选择一部分非欺诈样本
        fraud_indices = np.where(y == 1)[0]
        non_fraud_indices = np.where(y == 0)[0]
        
        # 选择非欺诈样本的数量，例如欺诈样本的五倍
        n_non_fraud = min(len(non_fraud_indices), len(fraud_indices) * 5)
        selected_non_fraud_indices = np.random.choice(non_fraud_indices, n_non_fraud, replace=False)
        
        # 合并所有欺诈样本和选择的非欺诈样本
        selected_indices = np.concatenate([fraud_indices, selected_non_fraud_indices])
        X = X[selected_indices]
        y = y[selected_indices]
        
        print(f"加载了 {len(fraud_indices)} 个欺诈样本和 {n_non_fraud} 个非欺诈样本")
        return X, y
    except Exception as e:
        print(f"加载数据集时出错: {e}")
        return None, None


def normalize_data(X):
    """
    对特征矩阵进行标准化
    
    参数:
        X (np.ndarray): 原始特征矩阵
        
    返回:
        X_normalized (np.ndarray): 标准化后的特征矩阵
        scaler (StandardScaler): 标准化器，用于后续转换
    """
    scaler = StandardScaler()
    X_normalized = scaler.fit_transform(X)
    return X_normalized, scaler


def select_features(X, y):
    """
    使用随机森林进行特征选择
    
    参数:
        X (np.ndarray): 特征矩阵
        y (np.ndarray): 标签向量
        
    返回:
        X_selected (np.ndarray): 选择后的特征矩阵
        selector: 特征选择器
    """
    print("进行特征选择...")
    selector = SelectFromModel(
        RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced'),
        threshold='median'
    )
    X_selected = selector.fit_transform(X, y)
    
    # 获取选择的特征索引
    selected_indices = selector.get_support(indices=True)
    print(f"从 {X.shape[1]} 个特征中选择了 {X_selected.shape[1]} 个特征")
    
    return X_selected, selector


def resample_data(X, y, method='smote_enn'):
    """
    使用不同的重采样方法处理不平衡数据
    
    参数:
        X (np.ndarray): 特征矩阵
        y (np.ndarray): 标签向量
        method (str): 重采样方法，可选['smote', 'adasyn', 'smote_enn', 'smote_tomek']
        
    返回:
        X_resampled (np.ndarray): 重采样后的特征矩阵
        y_resampled (np.ndarray): 重采样后的标签向量
    """
    print(f"使用 {method} 进行重采样...")
    
    if method == 'smote':
        resampler = SMOTE(random_state=42, k_neighbors=3)
    elif method == 'adasyn':
        resampler = ADASYN(random_state=42, n_neighbors=3)
    elif method == 'smote_enn':
        resampler = SMOTEENN(random_state=42, smote=SMOTE(k_neighbors=3))
    elif method == 'smote_tomek':
        resampler = SMOTETomek(random_state=42, smote=SMOTE(k_neighbors=3))
    else:
        raise ValueError(f"不支持的重采样方法: {method}")
    
    X_resampled, y_resampled = resampler.fit_resample(X, y)
    
    # 获取类别分布
    before_counts = np.bincount(y.astype(int))
    after_counts = np.bincount(y_resampled.astype(int))
    
    print(f"原始数据类别分布: [非欺诈: {before_counts[0]}, 欺诈: {before_counts[1]}]")
    print(f"重采样后类别分布: [非欺诈: {after_counts[0]}, 欺诈: {after_counts[1]}]")
    print(f"生成了 {len(y_resampled) - len(y)} 个合成样本")
    
    return X_resampled, y_resampled


def optimize_svm_parameters(X, y):
    """
    使用网格搜索优化SVM参数
    
    参数:
        X (np.ndarray): 特征矩阵
        y (np.ndarray): 标签向量
        
    返回:
        best_params (dict): 最佳参数
    """
    print("优化SVM参数...")
    
    # 定义参数网格
    param_grid = {
        'C': [0.1, 1, 10, 100],
        'gamma': ['scale', 'auto', 0.1, 0.01],
        'kernel': ['rbf', 'poly'],
        'class_weight': ['balanced', {0:1, 1:5}, {0:1, 1:10}]
    }
    
    # 创建网格搜索
    grid_search = GridSearchCV(
        SVC(probability=True, random_state=42),
        param_grid,
        cv=3,
        scoring='f1',
        n_jobs=-1
    )
    
    # 训练
    grid_search.fit(X, y)
    
    print(f"最佳参数: {grid_search.best_params_}")
    print(f"最佳分数: {grid_search.best_score_:.4f}")
    
    return grid_search.best_params_


def train_ensemble_model(X, y, svm_params):
    """
    训练集成模型
    
    参数:
        X (np.ndarray): 特征矩阵
        y (np.ndarray): 标签向量
        svm_params (dict): SVM参数
        
    返回:
        model: 训练好的模型
    """
    print("训练集成模型...")
    
    # 创建基础分类器
    svm = SVC(probability=True, random_state=42, **svm_params)
    rf = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        class_weight='balanced'
    )
    gb = GradientBoostingClassifier(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=5,
        random_state=42
    )
    
    # 创建投票分类器
    ensemble = VotingClassifier(
        estimators=[('svm', svm), ('rf', rf), ('gb', gb)],
        voting='soft'
    )
    
    # 训练模型
    ensemble.fit(X, y)
    
    return ensemble


def evaluate_model(model, X_test, y_test):
    """
    全面评估模型性能
    
    参数:
        model: 训练好的模型
        X_test (np.ndarray): 测试特征矩阵
        y_test (np.ndarray): 测试标签向量
        
    返回:
        metrics (dict): 评估指标
    """
    # 预测
    y_pred = model.predict(X_test)
    y_prob = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
    
    # 计算混淆矩阵
    tn, fp, fn, tp = confusion_matrix(y_test, y_pred).ravel()
    
    # 计算各项指标
    accuracy = accuracy_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred)
    recall = recall_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred)
    
    # 计算敏感度和特异度
    sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    
    # 计算G-mean
    g_mean = np.sqrt(sensitivity * specificity)
    
    # 计算AUC
    auc = roc_auc_score(y_test, y_prob) if y_prob is not None else 0.5
    
    # 汇总指标
    metrics = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'g_mean': g_mean,
        'auc': auc,
        'sensitivity': sensitivity,
        'specificity': specificity,
        'confusion_matrix': {
            'tn': tn,
            'fp': fp,
            'fn': fn,
            'tp': tp
        }
    }
    
    return metrics


def plot_confusion_matrix(cm, title='混淆矩阵'):
    """
    绘制混淆矩阵
    
    参数:
        cm (dict): 混淆矩阵
        title (str): 图表标题
    """
    plt.figure(figsize=(8, 6))
    cm_array = np.array([[cm['tn'], cm['fp']], [cm['fn'], cm['tp']]])
    
    plt.imshow(cm_array, interpolation='nearest', cmap=plt.cm.Blues)
    plt.title(title)
    plt.colorbar()
    
    classes = ['非欺诈 (0)', '欺诈 (1)']
    tick_marks = np.arange(len(classes))
    plt.xticks(tick_marks, classes, rotation=45)
    plt.yticks(tick_marks, classes)
    
    # 添加文本注释
    thresh = cm_array.max() / 2.
    for i in range(2):
        for j in range(2):
            plt.text(j, i, format(cm_array[i, j], 'd'),
                     horizontalalignment="center",
                     color="white" if cm_array[i, j] > thresh else "black")
    
    plt.tight_layout()
    plt.ylabel('真实标签')
    plt.xlabel('预测标签')
    
    # 保存图表
    os.makedirs('results', exist_ok=True)
    plt.savefig('results/confusion_matrix.png')
    plt.close()


def main():
    """
    主函数
    """
    # 设置随机种子
    np.random.seed(42)
    
    # 加载数据集
    X, y = load_creditcard_dataset()
    if X is None or y is None:
        print("无法加载数据集，请检查数据集路径")
        return
    
    # 数据预处理
    X, scaler = normalize_data(X)
    
    # 特征选择
    X, selector = select_features(X, y)
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print("\n=== 改进的信用卡欺诈检测模型 ===")
    
    # 使用SMOTE-ENN进行重采样
    X_resampled, y_resampled = resample_data(X_train, y_train, method='smote_enn')
    
    # 优化SVM参数
    svm_params = optimize_svm_parameters(X_resampled, y_resampled)
    
    # 训练集成模型
    model = train_ensemble_model(X_resampled, y_resampled, svm_params)
    
    # 评估模型
    print("\n正在评估模型...")
    start_time = time.time()
    metrics = evaluate_model(model, X_test, y_test)
    end_time = time.time()
    
    # 输出评估结果
    print("\n=== 评估结果 ===")
    print(f"accuracy: {metrics['accuracy']:.4f}")
    print(f"precision: {metrics['precision']:.4f}")
    print(f"recall: {metrics['recall']:.4f}")
    print(f"f1_score: {metrics['f1_score']:.4f}")
    print(f"g_mean: {metrics['g_mean']:.4f}")
    print(f"auc: {metrics['auc']:.4f}")
    print(f"sensitivity: {metrics['sensitivity']:.4f}")
    print(f"specificity: {metrics['specificity']:.4f}")
    print(f"evaluate_and_compare 执行时间: {end_time - start_time:.2f} 秒")
    
    # 输出混淆矩阵
    cm = metrics['confusion_matrix']
    print("\n混淆矩阵:")
    print(f"真正例(TP): {cm['tp']}")
    print(f"假正例(FP): {cm['fp']}")
    print(f"真负例(TN): {cm['tn']}")
    print(f"假负例(FN): {cm['fn']}")
    
    # 绘制混淆矩阵
    plot_confusion_matrix(cm)
    print("\n混淆矩阵图已保存到 results/confusion_matrix.png")


if __name__ == '__main__':
    main()
