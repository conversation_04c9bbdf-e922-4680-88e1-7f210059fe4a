# GA优化ADASYN参数的WGAN-SVM混合框架

本项目实现了一个用于解决不平衡分类问题的混合框架，该框架结合了遗传算法（GA）、自适应合成采样（ADASYN）、Wasserstein生成对抗网络（WGAN）和支持向量机（SVM）的优势。

## 框架概述

该框架主要包含以下创新点：

1. **GA优化ADASYN参数**：使用遗传算法优化ADASYN的关键参数，包括平衡系数β、近邻数k、权值计算参数δ以及代价敏感惩罚因子C+和C-。

2. **WGAN生成高质量样本**：使用ADASYN生成的少数类样本作为WGAN的输入，生成符合原始数据分布的高质量样本。

3. **权值计算与代价敏感训练**：根据样本到类中心的距离动态分配权重，并结合代价敏感SVM进行分类。

## 目录结构

```
.
├── main.py                  # 主程序入口
├── data_preprocessing.py    # 数据预处理模块
├── adasyn.py                # ADASYN算法实现
├── wgan.py                  # WGAN模型实现
├── svm_model.py             # 代价敏感SVM模型
├── genetic_algorithm.py     # 遗传算法优化模块
├── evaluation.py            # 评估指标模块
├── utils.py                 # 工具函数模块
└── README.md                # 项目说明文档
```

## 安装依赖

本项目需要以下Python库：

```bash
pip install numpy pandas matplotlib scikit-learn tensorflow imbalanced-learn seaborn
```

## 使用方法

### 基本用法

```bash
python main.py --dataset glass6 --mode full --visualize
```

### 参数说明

- `--dataset`：数据集名称，默认为'glass6'
- `--test_size`：测试集比例，默认为0.2
- `--random_state`：随机种子，默认为42

- `--ga_pop_size`：GA种群大小，默认为30
- `--ga_n_generations`：GA迭代代数，默认为20
- `--ga_crossover_rate`：GA交叉概率，默认为0.8
- `--ga_mutation_rate`：GA变异概率，默认为0.1
- `--ga_elite_size`：GA精英数量，默认为3

- `--wgan_latent_dim`：WGAN潜在空间维度，默认为100
- `--wgan_n_critic`：WGAN判别器训练次数，默认为5
- `--wgan_clip_value`：WGAN权重裁剪值，默认为0.01
- `--wgan_learning_rate`：WGAN学习率，默认为0.00005
- `--wgan_batch_size`：WGAN批量大小，默认为64
- `--wgan_epochs`：WGAN训练轮数，默认为1000

- `--n_folds`：交叉验证折数，默认为5

- `--output_dir`：输出目录，默认为'results'
- `--save_model`：是否保存模型，默认为False
- `--visualize`：是否可视化数据，默认为False

- `--mode`：运行模式，可选['full', 'ga_only', 'wgan_only', 'compare']，默认为'full'

### 运行模式

- `full`：完整运行GA优化ADASYN参数的WGAN-SVM混合框架
- `ga_only`：仅运行GA优化ADASYN参数部分
- `wgan_only`：使用默认ADASYN参数，运行WGAN-SVM部分
- `compare`：比较提出的方法与基准方法（SVM、ADASYN-SVM、WGAN-SVM）

## 示例

### 完整运行

```bash
python main.py --dataset glass6 --mode full --visualize --save_model
```

### 比较模式

```bash
python main.py --dataset yeast1.7 --mode compare --visualize --output_dir results/yeast1.7
```

## 数据集

本框架支持以下公开数据集：

- glass6（IR=6.4）
- yeast1.7（IR=14.3）
- 其他imbalanced-learn库支持的不平衡数据集

也可以通过在`data/`目录下放置自定义CSV格式数据集使用。

## 输出结果

- 模型性能指标：准确率、精确率、召回率、F1分数、G-mean、AUC等
- 可视化结果：原始数据分布、ADASYN重采样后数据分布、WGAN生成样本对比、模型性能比较等
- 训练好的模型（如果使用`--save_model`参数）

## 参考文献

1. He, H., Bai, Y., Garcia, E. A., & Li, S. (2008). ADASYN: Adaptive synthetic sampling approach for imbalanced learning. In 2008 IEEE International Joint Conference on Neural Networks (pp. 1322-1328).

2. Arjovsky, M., Chintala, S., & Bottou, L. (2017). Wasserstein GAN. arXiv preprint arXiv:1701.07875.

3. Tang, B., & He, H. (2015). KernelADASYN: Kernel based adaptive synthetic data generation for imbalanced learning. In 2015 IEEE Congress on Evolutionary Computation (CEC) (pp. 664-671).

4. Cortes, C., & Vapnik, V. (1995). Support-vector networks. Machine learning, 20(3), 273-297.
