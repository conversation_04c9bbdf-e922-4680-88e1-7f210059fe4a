"""
使用ADASYN处理信用卡欺诈检测数据集
"""

import numpy as np
import pandas as pd
import time
import os
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.svm import SVC
from imblearn.over_sampling import ADASYN

# 设置环境变量，避免joblib警告
os.environ["LOKY_MAX_CPU_COUNT"] = "4"


def load_creditcard_dataset():
    """
    加载信用卡欺诈检测数据集
    
    返回:
        X (np.ndarray): 特征矩阵
        y (np.ndarray): 标签向量
    """
    print("加载信用卡欺诈检测数据集...")
    try:
        data = pd.read_csv('../data/creditcard.csv')
        # 分离特征和标签
        X = data.drop('Class', axis=1).values
        y = data['Class'].values
        
        # 由于信用卡数据集很大，可以考虑只使用一部分数据
        # 保持所有欺诈样本，随机选择一部分非欺诈样本
        fraud_indices = np.where(y == 1)[0]
        non_fraud_indices = np.where(y == 0)[0]
        
        # 选择非欺诈样本的数量，例如欺诈样本的五倍
        n_non_fraud = min(len(non_fraud_indices), len(fraud_indices) * 5)
        selected_non_fraud_indices = np.random.choice(non_fraud_indices, n_non_fraud, replace=False)
        
        # 合并所有欺诈样本和选择的非欺诈样本
        selected_indices = np.concatenate([fraud_indices, selected_non_fraud_indices])
        X = X[selected_indices]
        y = y[selected_indices]
        
        print(f"加载了 {len(fraud_indices)} 个欺诈样本和 {n_non_fraud} 个非欺诈样本")
        return X, y
    except Exception as e:
        print(f"加载数据集时出错: {e}")
        return None, None


def normalize_data(X):
    """
    对特征矩阵进行标准化
    
    参数:
        X (np.ndarray): 原始特征矩阵
        
    返回:
        X_normalized (np.ndarray): 标准化后的特征矩阵
        scaler (StandardScaler): 标准化器，用于后续转换
    """
    scaler = StandardScaler()
    X_normalized = scaler.fit_transform(X)
    return X_normalized, scaler


def calculate_confusion_matrix(y_true, y_pred):
    """
    计算混淆矩阵
    
    参数:
        y_true (np.ndarray): 真实标签
        y_pred (np.ndarray): 预测标签
        
    返回:
        tn (int): 真负例数量
        fp (int): 假正例数量
        fn (int): 假负例数量
        tp (int): 真正例数量
    """
    from sklearn.metrics import confusion_matrix
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
    return tn, fp, fn, tp


def evaluate_model(model, X_test, y_test):
    """
    全面评估模型性能
    
    参数:
        model: 训练好的模型
        X_test (np.ndarray): 测试特征矩阵
        y_test (np.ndarray): 测试标签向量
        
    返回:
        metrics (dict): 评估指标
    """
    from sklearn.metrics import (
        accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
    )
    
    # 预测
    y_pred = model.predict(X_test)
    y_prob = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
    
    # 计算混淆矩阵
    tn, fp, fn, tp = calculate_confusion_matrix(y_test, y_pred)
    
    # 计算各项指标
    accuracy = accuracy_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred)
    recall = recall_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred)
    
    # 计算敏感度和特异度
    sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    
    # 计算G-mean
    g_mean = np.sqrt(sensitivity * specificity)
    
    # 计算AUC
    auc = roc_auc_score(y_test, y_prob) if y_prob is not None else 0.5
    
    # 汇总指标
    metrics = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'g_mean': g_mean,
        'auc': auc,
        'sensitivity': sensitivity,
        'specificity': specificity,
        'confusion_matrix': {
            'tn': tn,
            'fp': fp,
            'fn': fn,
            'tp': tp
        }
    }
    
    return metrics


def main():
    """
    主函数
    """
    # 设置随机种子
    np.random.seed(42)
    
    # 加载数据集
    X, y = load_creditcard_dataset()
    if X is None or y is None:
        print("无法加载数据集，请检查数据集路径")
        return
    
    # 数据预处理
    X, scaler = normalize_data(X)
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print("\n=== 使用ADASYN处理信用卡欺诈检测数据集 ===")
    
    # 使用ADASYN生成合成样本
    print("\n正在使用ADASYN生成合成样本...")
    adasyn = ADASYN(random_state=42)
    X_resampled, y_resampled = adasyn.fit_resample(X_train, y_train)
    
    # 获取类别分布
    before_counts = np.bincount(y_train.astype(int))
    after_counts = np.bincount(y_resampled.astype(int))
    
    print(f"原始训练集类别分布: [非欺诈: {before_counts[0]}, 欺诈: {before_counts[1]}]")
    print(f"重采样后类别分布: [非欺诈: {after_counts[0]}, 欺诈: {after_counts[1]}]")
    print(f"生成了 {len(y_resampled) - len(y_train)} 个合成样本")
    
    # 训练SVM模型
    print("\n正在训练SVM模型...")
    svm = SVC(probability=True, random_state=42)
    svm.fit(X_resampled, y_resampled)
    
    # 评估模型
    print("\n正在评估模型...")
    start_time = time.time()
    metrics = evaluate_model(svm, X_test, y_test)
    end_time = time.time()
    
    # 输出评估结果
    print("\n=== 评估结果 ===")
    print(f"accuracy: {metrics['accuracy']:.4f}")
    print(f"precision: {metrics['precision']:.4f}")
    print(f"recall: {metrics['recall']:.4f}")
    print(f"f1_score: {metrics['f1_score']:.4f}")
    print(f"g_mean: {metrics['g_mean']:.4f}")
    print(f"auc: {metrics['auc']:.4f}")
    print(f"sensitivity: {metrics['sensitivity']:.4f}")
    print(f"specificity: {metrics['specificity']:.4f}")
    print(f"evaluate_and_compare 执行时间: {end_time - start_time:.2f} 秒")
    
    # 输出混淆矩阵
    cm = metrics['confusion_matrix']
    print("\n混淆矩阵:")
    print(f"真正例(TP): {cm['tp']}")
    print(f"假正例(FP): {cm['fp']}")
    print(f"真负例(TN): {cm['tn']}")
    print(f"假负例(FN): {cm['fn']}")


if __name__ == '__main__':
    main()
