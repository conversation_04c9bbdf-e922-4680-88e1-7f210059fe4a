@echo off
echo GA优化ADASYN参数的WGAN-SVM混合框架 - 信用卡欺诈检测
echo =====================================================

:menu
echo.
echo 请选择运行模式:
echo 1. 完整模式 (GA优化 + WGAN + SVM)
echo 2. 仅GA优化模式
echo 3. 仅WGAN模式
echo 4. 比较模式 (与基准方法比较)
echo 5. 退出
echo.

set /p choice=请输入选项编号 (1-5):

if "%choice%"=="1" goto full
if "%choice%"=="2" goto ga_only
if "%choice%"=="3" goto wgan_only
if "%choice%"=="4" goto compare
if "%choice%"=="5" goto end

echo 无效选项，请重新选择
goto menu

:full
echo 正在运行完整模式...
python main.py --dataset creditcard --mode full --visualize
goto end

:ga_only
echo 正在运行仅GA优化模式...
python main.py --dataset creditcard --mode ga_only --visualize
goto end

:wgan_only
echo 正在运行仅WGAN模式...
python main.py --dataset creditcard --mode wgan_only --visualize
goto end

:compare
echo 正在运行比较模式...
python main.py --dataset creditcard --mode compare --visualize
goto end

:end
echo.
echo 程序执行完毕，结果保存在 results/creditcard 目录下
pause
