"""
数据预处理模块
包含数据加载、归一化和划分功能
"""

import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from imblearn.datasets import fetch_datasets


def load_dataset(dataset_name):
    """
    加载指定的不平衡数据集

    参数:
        dataset_name (str): 数据集名称，如'glass6', 'yeast1.7', 'creditcard'等

    返回:
        X (np.ndarray): 特征矩阵
        y (np.ndarray): 标签向量
    """
    try:
        # 处理特殊数据集
        if dataset_name.lower() == 'creditcard':
            print("加载信用卡欺诈检测数据集...")
            data = pd.read_csv('data/creditcard.csv')
            # 分离特征和标签
            X = data.drop('Class', axis=1).values
            y = data['Class'].values

            # 由于信用卡数据集很大，可以考虑只使用一部分数据
            # 保持所有欺诈样本，随机选择一部分非欺诈样本
            fraud_indices = np.where(y == 1)[0]
            non_fraud_indices = np.where(y == 0)[0]

            # 选择非欺诈样本的数量，例如欺诈样本的五倍
            n_non_fraud = min(len(non_fraud_indices), len(fraud_indices) * 5)
            selected_non_fraud_indices = np.random.choice(non_fraud_indices, n_non_fraud, replace=False)

            # 合并所有欺诈样本和选择的非欺诈样本
            selected_indices = np.concatenate([fraud_indices, selected_non_fraud_indices])
            X = X[selected_indices]
            y = y[selected_indices]

            print(f"加载了 {len(fraud_indices)} 个欺诈样本和 {n_non_fraud} 个非欺诈样本")
            return X, y

        # 尝试从imblearn加载数据集
        datasets = fetch_datasets()
        if dataset_name in datasets:
            X, y = datasets[dataset_name].data, datasets[dataset_name].target
            return X, y
        else:
            # 如果imblearn中没有，尝试从本地加载
            data = pd.read_csv(f'data/{dataset_name}.csv')
            X = data.iloc[:, :-1].values
            y = data.iloc[:, -1].values
            return X, y
    except Exception as e:
        print(f"加载数据集 {dataset_name} 时出错: {e}")
        return None, None


def normalize_data(X):
    """
    对特征矩阵进行标准化

    参数:
        X (np.ndarray): 原始特征矩阵

    返回:
        X_normalized (np.ndarray): 标准化后的特征矩阵
        scaler (StandardScaler): 标准化器，用于后续转换
    """
    scaler = StandardScaler()
    X_normalized = scaler.fit_transform(X)
    return X_normalized, scaler


def split_data(X, y, test_size=0.2, random_state=42):
    """
    将数据集划分为训练集和测试集

    参数:
        X (np.ndarray): 特征矩阵
        y (np.ndarray): 标签向量
        test_size (float): 测试集比例，默认0.2
        random_state (int): 随机种子，默认42

    返回:
        X_train, X_test, y_train, y_test: 划分后的训练集和测试集
    """
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=y
    )
    return X_train, X_test, y_train, y_test


def get_class_distribution(y):
    """
    获取数据集的类别分布

    参数:
        y (np.ndarray): 标签向量

    返回:
        class_counts (dict): 各类别的样本数量
        imbalance_ratio (float): 不平衡比例 (多数类样本数/少数类样本数)
    """
    unique_classes, class_counts = np.unique(y, return_counts=True)
    class_counts_dict = dict(zip(unique_classes, class_counts))

    # 计算不平衡比例
    majority_class = max(class_counts_dict, key=class_counts_dict.get)
    minority_class = min(class_counts_dict, key=class_counts_dict.get)
    imbalance_ratio = class_counts_dict[majority_class] / class_counts_dict[minority_class]

    return class_counts_dict, imbalance_ratio


def get_class_indices(y):
    """
    获取各类别样本的索引

    参数:
        y (np.ndarray): 标签向量

    返回:
        class_indices (dict): 各类别样本的索引
    """
    unique_classes = np.unique(y)
    class_indices = {}

    for cls in unique_classes:
        class_indices[cls] = np.where(y == cls)[0]

    return class_indices
