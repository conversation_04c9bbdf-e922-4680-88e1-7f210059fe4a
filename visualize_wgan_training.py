"""
WGAN训练损失函数可视化脚本
专门用于生成详细的训练损失函数图
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import os
import time
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from imblearn.over_sampling import ADASYN

# 设置环境变量，避免joblib警告
os.environ["LOKY_MAX_CPU_COUNT"] = "4"

# 导入自定义模块
from data_preprocessing import load_dataset, normalize_data, get_class_distribution
from adasyn import ParameterizedADASYN
from wgan import WGAN


def load_creditcard_dataset():
    """
    加载信用卡欺诈检测数据集
    
    返回:
        X (np.ndarray): 特征矩阵
        y (np.ndarray): 标签向量
    """
    print("加载信用卡欺诈检测数据集...")
    try:
        data = pd.read_csv('data/creditcard.csv')
        # 分离特征和标签
        X = data.drop('Class', axis=1).values
        y = data['Class'].values
        
        # 由于信用卡数据集很大，可以考虑只使用一部分数据
        # 保持所有欺诈样本，随机选择一部分非欺诈样本
        fraud_indices = np.where(y == 1)[0]
        non_fraud_indices = np.where(y == 0)[0]
        
        # 选择非欺诈样本的数量，例如欺诈样本的五倍
        n_non_fraud = min(len(non_fraud_indices), len(fraud_indices) * 5)
        selected_non_fraud_indices = np.random.choice(non_fraud_indices, n_non_fraud, replace=False)
        
        # 合并所有欺诈样本和选择的非欺诈样本
        selected_indices = np.concatenate([fraud_indices, selected_non_fraud_indices])
        X = X[selected_indices]
        y = y[selected_indices]
        
        print(f"加载了 {len(fraud_indices)} 个欺诈样本和 {n_non_fraud} 个非欺诈样本")
        return X, y
    except Exception as e:
        print(f"加载数据集时出错: {e}")
        return None, None


def create_comprehensive_loss_plot(history, save_path, dataset_name="creditcard"):
    """
    创建综合的损失函数图
    
    参数:
        history (dict): 训练历史
        save_path (str): 保存路径
        dataset_name (str): 数据集名称
    """
    epochs = range(1, len(history['critic_loss']) + 1)
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建3x2的子图
    fig, axes = plt.subplots(3, 2, figsize=(16, 18))
    fig.suptitle(f'{dataset_name.upper()} 数据集 - WGAN训练损失函数详细分析', fontsize=18, fontweight='bold')
    
    # 子图1：生成器和判别器损失对比
    axes[0, 0].plot(epochs, history['critic_loss'], 'b-', label='判别器损失 (Critic Loss)', linewidth=2, alpha=0.8)
    axes[0, 0].plot(epochs, history['generator_loss'], 'r-', label='生成器损失 (Generator Loss)', linewidth=2, alpha=0.8)
    axes[0, 0].set_title('生成器与判别器损失对比', fontsize=14, fontweight='bold')
    axes[0, 0].set_xlabel('训练轮次 (Epoch)')
    axes[0, 0].set_ylabel('损失值 (Loss)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].set_xlim(0, len(epochs))
    
    # 子图2：判别器对真实样本和假样本的损失
    if 'critic_loss_real' in history and 'critic_loss_fake' in history:
        axes[0, 1].plot(epochs, history['critic_loss_real'], 'g-', label='真实样本损失', linewidth=2, alpha=0.8)
        axes[0, 1].plot(epochs, history['critic_loss_fake'], 'orange', label='假样本损失', linewidth=2, alpha=0.8)
        axes[0, 1].set_title('判别器对真实样本与假样本的损失', fontsize=14, fontweight='bold')
        axes[0, 1].set_xlabel('训练轮次 (Epoch)')
        axes[0, 1].set_ylabel('损失值 (Loss)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].set_xlim(0, len(epochs))
    
    # 子图3：Wasserstein距离
    if 'wasserstein_distance' in history:
        axes[1, 0].plot(epochs, history['wasserstein_distance'], 'purple', label='Wasserstein距离', linewidth=2, alpha=0.8)
        axes[1, 0].set_title('Wasserstein距离变化', fontsize=14, fontweight='bold')
        axes[1, 0].set_xlabel('训练轮次 (Epoch)')
        axes[1, 0].set_ylabel('Wasserstein距离')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].set_xlim(0, len(epochs))
    
    # 子图4：损失收敛性分析（移动平均）
    window_size = min(50, len(history['critic_loss']) // 10)
    if window_size > 1:
        critic_ma = np.convolve(history['critic_loss'], np.ones(window_size)/window_size, mode='valid')
        generator_ma = np.convolve(history['generator_loss'], np.ones(window_size)/window_size, mode='valid')
        ma_epochs = range(window_size, len(history['critic_loss']) + 1)
        
        axes[1, 1].plot(ma_epochs, critic_ma, 'b-', label=f'判别器损失(MA-{window_size})', linewidth=2, alpha=0.8)
        axes[1, 1].plot(ma_epochs, generator_ma, 'r-', label=f'生成器损失(MA-{window_size})', linewidth=2, alpha=0.8)
        axes[1, 1].set_title('损失收敛性分析（移动平均）', fontsize=14, fontweight='bold')
        axes[1, 1].set_xlabel('训练轮次 (Epoch)')
        axes[1, 1].set_ylabel('损失值 (Loss)')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].set_xlim(0, len(epochs))
    
    # 子图5：损失差值分析
    loss_diff = np.array(history['generator_loss']) - np.array(history['critic_loss'])
    axes[2, 0].plot(epochs, loss_diff, 'darkgreen', label='生成器-判别器损失差', linewidth=2, alpha=0.8)
    axes[2, 0].axhline(y=0, color='black', linestyle='--', alpha=0.5)
    axes[2, 0].set_title('生成器与判别器损失差值', fontsize=14, fontweight='bold')
    axes[2, 0].set_xlabel('训练轮次 (Epoch)')
    axes[2, 0].set_ylabel('损失差值')
    axes[2, 0].legend()
    axes[2, 0].grid(True, alpha=0.3)
    axes[2, 0].set_xlim(0, len(epochs))
    
    # 子图6：训练稳定性分析
    # 计算损失的标准差（滑动窗口）
    window_size_std = min(100, len(history['critic_loss']) // 5)
    if window_size_std > 1:
        critic_std = []
        generator_std = []
        std_epochs = []
        
        for i in range(window_size_std, len(history['critic_loss'])):
            critic_window = history['critic_loss'][i-window_size_std:i]
            generator_window = history['generator_loss'][i-window_size_std:i]
            critic_std.append(np.std(critic_window))
            generator_std.append(np.std(generator_window))
            std_epochs.append(i)
        
        axes[2, 1].plot(std_epochs, critic_std, 'b-', label=f'判别器损失标准差(窗口={window_size_std})', linewidth=2, alpha=0.8)
        axes[2, 1].plot(std_epochs, generator_std, 'r-', label=f'生成器损失标准差(窗口={window_size_std})', linewidth=2, alpha=0.8)
        axes[2, 1].set_title('训练稳定性分析（损失标准差）', fontsize=14, fontweight='bold')
        axes[2, 1].set_xlabel('训练轮次 (Epoch)')
        axes[2, 1].set_ylabel('损失标准差')
        axes[2, 1].legend()
        axes[2, 1].grid(True, alpha=0.3)
        axes[2, 1].set_xlim(0, len(epochs))
    
    plt.tight_layout()
    
    # 确保目录存在
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"详细训练损失函数图已保存到: {save_path}")
    plt.close()


def main():
    """
    主函数 - 专门用于可视化WGAN训练过程
    """
    # 设置随机种子
    np.random.seed(42)
    
    print("=== WGAN训练损失函数可视化 ===")
    
    # 加载数据集
    X, y = load_creditcard_dataset()
    if X is None or y is None:
        print("无法加载数据集，请检查数据集路径")
        return
    
    # 数据预处理
    scaler = StandardScaler()
    X = scaler.fit_transform(X)
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # 使用ADASYN生成合成样本
    print("\n正在使用ADASYN生成合成样本...")
    adasyn = ADASYN(random_state=42)
    X_resampled, y_resampled = adasyn.fit_resample(X_train, y_train)
    
    # 获取少数类样本
    minority_class = min(get_class_distribution(y_resampled)[0], key=get_class_distribution(y_resampled)[0].get)
    minority_indices = np.where(y_resampled == minority_class)[0]
    X_minority = X_resampled[minority_indices]
    
    # 获取ADASYN生成的样本（假设是重采样后多出来的部分）
    original_minority_count = np.sum(y_train == minority_class)
    adasyn_samples = X_minority[original_minority_count:]
    
    print(f"使用 {len(adasyn_samples)} 个ADASYN生成的样本作为WGAN生成器的输入")
    
    # 初始化WGAN
    wgan = WGAN(
        input_dim=X_resampled.shape[1],
        latent_dim=50,  # 适中的潜在空间维度
        clip_value=0.01,
        n_critic=3,
        learning_rate=0.0001
    )
    
    # 训练WGAN
    print("\n正在训练WGAN...")
    start_time = time.time()
    
    history = wgan.train_with_adasyn_samples(
        X_minority[:original_minority_count],  # 使用原始少数类样本作为真实样本
        adasyn_samples,  # 使用ADASYN生成的样本作为生成器输入
        batch_size=32,
        epochs=500,  # 增加训练轮数以观察收敛过程
        verbose=1
    )
    
    end_time = time.time()
    print(f"\nWGAN训练完成，耗时: {end_time - start_time:.2f} 秒")
    
    # 创建输出目录
    output_dir = "results/wgan_visualization"
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成详细的损失函数图
    create_comprehensive_loss_plot(
        history, 
        os.path.join(output_dir, 'comprehensive_wgan_loss_analysis.png'),
        dataset_name="creditcard"
    )
    
    # 使用WGAN类的可视化方法
    wgan.plot_training_history(
        history, 
        save_path=os.path.join(output_dir, 'wgan_training_loss.png'),
        title_prefix="ADASYN-WGAN"
    )
    
    # 分析训练收敛性
    convergence_analysis = wgan.analyze_training_convergence(history)
    
    # 保存收敛性分析结果
    with open(os.path.join(output_dir, 'convergence_analysis.txt'), 'w', encoding='utf-8') as f:
        f.write("=== WGAN训练收敛性分析 ===\n")
        f.write(f"最终判别器损失: {convergence_analysis['final_critic_loss']:.6f}\n")
        f.write(f"最终生成器损失: {convergence_analysis['final_generator_loss']:.6f}\n")
        f.write(f"判别器损失稳定性: {convergence_analysis['critic_loss_stability']:.6f}\n")
        f.write(f"生成器损失稳定性: {convergence_analysis['generator_loss_stability']:.6f}\n")
        f.write(f"是否收敛: {'是' if convergence_analysis['is_converged'] else '否'}\n")
        f.write(f"是否达到纳什平衡: {'是' if convergence_analysis['nash_equilibrium'] else '否'}\n")
        
        if 'critic_improvement' in convergence_analysis:
            f.write(f"判别器损失改进: {convergence_analysis['critic_improvement']:.6f}\n")
            f.write(f"生成器损失改进: {convergence_analysis['generator_improvement']:.6f}\n")
    
    print(f"\n=== WGAN训练收敛性分析 ===")
    print(f"最终判别器损失: {convergence_analysis['final_critic_loss']:.6f}")
    print(f"最终生成器损失: {convergence_analysis['final_generator_loss']:.6f}")
    print(f"判别器损失稳定性: {convergence_analysis['critic_loss_stability']:.6f}")
    print(f"生成器损失稳定性: {convergence_analysis['generator_loss_stability']:.6f}")
    print(f"是否收敛: {'是' if convergence_analysis['is_converged'] else '否'}")
    print(f"是否达到纳什平衡: {'是' if convergence_analysis['nash_equilibrium'] else '否'}")

    # 生成样本质量评估
    print("\n正在生成样本并评估质量...")
    X_wgan = wgan.generate_samples_from_adasyn(adasyn_samples)

    # 可视化样本分布对比
    if X_minority.shape[1] > 2:
        from sklearn.decomposition import PCA
        pca = PCA(n_components=2)
        X_combined = np.vstack([X_minority[:original_minority_count], adasyn_samples, X_wgan])
        X_pca = pca.fit_transform(X_combined)

        plt.figure(figsize=(12, 8))
        plt.scatter(X_pca[:original_minority_count, 0], X_pca[:original_minority_count, 1],
                  c='blue', label='原始少数类样本', alpha=0.7, s=50)
        plt.scatter(X_pca[original_minority_count:original_minority_count+len(adasyn_samples), 0],
                  X_pca[original_minority_count:original_minority_count+len(adasyn_samples), 1],
                  c='green', label='ADASYN生成的样本', alpha=0.7, s=50)
        plt.scatter(X_pca[original_minority_count+len(adasyn_samples):, 0],
                  X_pca[original_minority_count+len(adasyn_samples):, 1],
                  c='red', label='WGAN生成的样本', alpha=0.7, s=50)
        plt.title('原始样本、ADASYN样本和WGAN样本的分布对比 (PCA降维)', fontsize=14, fontweight='bold')
        plt.xlabel('第一主成分')
        plt.ylabel('第二主成分')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(output_dir, 'samples_distribution_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()

    print(f"\n所有可视化结果已保存到: {output_dir}")
    print("包含以下文件:")
    print("- comprehensive_wgan_loss_analysis.png: 综合损失分析图")
    print("- wgan_training_loss.png: WGAN训练损失图")
    print("- samples_distribution_comparison.png: 样本分布对比图")
    print("- convergence_analysis.txt: 收敛性分析报告")


if __name__ == '__main__':
    main()
