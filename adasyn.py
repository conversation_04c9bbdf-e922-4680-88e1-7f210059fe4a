"""
ADASYN算法模块
实现参数化的ADASYN算法，用于生成少数类样本
"""

import numpy as np
from sklearn.neighbors import NearestNeighbors
from collections import Counter


class ParameterizedADASYN:
    """
    参数化的ADASYN算法实现
    
    参数:
        beta (float): 平衡系数，控制生成样本数量
        k (int): 近邻数，用于计算样本局部密度
        delta (float): 权值计算参数，影响样本权重分配
        random_state (int): 随机种子
    """
    
    def __init__(self, beta=1.0, k=5, delta=0.1, random_state=None):
        self.beta = beta
        self.k = k
        self.delta = delta
        self.random_state = random_state
        self.minority_samples = None
        self.majority_samples = None
        self.minority_class = None
        self.majority_class = None
        
    def fit_resample(self, X, y):
        """
        使用ADASYN算法生成合成样本
        
        参数:
            X (np.ndarray): 特征矩阵
            y (np.ndarray): 标签向量
            
        返回:
            X_resampled (np.ndarray): 重采样后的特征矩阵
            y_resampled (np.ndarray): 重采样后的标签向量
        """
        if self.random_state is not None:
            np.random.seed(self.random_state)
            
        # 获取类别信息
        class_counts = Counter(y)
        self.minority_class = min(class_counts, key=class_counts.get)
        self.majority_class = max(class_counts, key=class_counts.get)
        
        # 获取少数类和多数类样本
        minority_indices = np.where(y == self.minority_class)[0]
        majority_indices = np.where(y == self.majority_class)[0]
        self.minority_samples = X[minority_indices]
        self.majority_samples = X[majority_indices]
        
        # 计算需要生成的样本数量
        m_minority = len(minority_indices)
        m_majority = len(majority_indices)
        G = int((m_majority - m_minority) * self.beta)
        
        if G <= 0:
            return X.copy(), y.copy()
        
        # 计算少数类样本的局部密度
        density_ratios = self._calculate_density_ratios(X, y)
        
        # 计算每个少数类样本需要生成的样本数量
        num_samples_to_generate = self._calculate_samples_per_instance(density_ratios, G)
        
        # 生成合成样本
        X_synthetic, y_synthetic = self._generate_synthetic_samples(
            num_samples_to_generate, X, y
        )
        
        # 合并原始样本和合成样本
        X_resampled = np.vstack((X, X_synthetic))
        y_resampled = np.hstack((y, y_synthetic))
        
        return X_resampled, y_resampled
    
    def _calculate_density_ratios(self, X, y):
        """
        计算少数类样本的局部密度比率
        
        参数:
            X (np.ndarray): 特征矩阵
            y (np.ndarray): 标签向量
            
        返回:
            density_ratios (np.ndarray): 每个少数类样本的密度比率
        """
        minority_indices = np.where(y == self.minority_class)[0]
        
        # 为每个少数类样本找到k个最近邻
        nn = NearestNeighbors(n_neighbors=self.k+1)
        nn.fit(X)
        distances, indices = nn.kneighbors(X[minority_indices])
        
        # 计算每个少数类样本的密度比率
        density_ratios = np.zeros(len(minority_indices))
        for i, idx in enumerate(indices):
            # 去掉第一个邻居（样本本身）
            neighbors = idx[1:]
            # 计算多数类邻居的数量
            majority_neighbors = np.sum(y[neighbors] == self.majority_class)
            # 计算密度比率
            density_ratios[i] = majority_neighbors / self.k
            
        # 归一化密度比率
        if np.sum(density_ratios) > 0:
            density_ratios = density_ratios / np.sum(density_ratios)
            
        return density_ratios
    
    def _calculate_samples_per_instance(self, density_ratios, G):
        """
        计算每个少数类样本需要生成的样本数量
        
        参数:
            density_ratios (np.ndarray): 每个少数类样本的密度比率
            G (int): 需要生成的总样本数量
            
        返回:
            num_samples (np.ndarray): 每个少数类样本需要生成的样本数量
        """
        num_samples = np.round(density_ratios * G).astype(int)
        # 确保总数等于G
        diff = G - np.sum(num_samples)
        if diff > 0:
            # 如果总数小于G，增加一些样本
            indices = np.argsort(density_ratios)[-int(diff):]
            num_samples[indices] += 1
        elif diff < 0:
            # 如果总数大于G，减少一些样本
            indices = np.argsort(density_ratios)[:int(-diff)]
            num_samples[indices] -= 1
            
        return num_samples
    
    def _generate_synthetic_samples(self, num_samples_to_generate, X, y):
        """
        生成合成样本
        
        参数:
            num_samples_to_generate (np.ndarray): 每个少数类样本需要生成的样本数量
            X (np.ndarray): 特征矩阵
            y (np.ndarray): 标签向量
            
        返回:
            X_synthetic (np.ndarray): 合成样本的特征矩阵
            y_synthetic (np.ndarray): 合成样本的标签向量
        """
        minority_indices = np.where(y == self.minority_class)[0]
        X_minority = X[minority_indices]
        
        # 为每个少数类样本找到k个最近邻
        nn = NearestNeighbors(n_neighbors=self.k+1)
        nn.fit(X_minority)
        distances, indices = nn.kneighbors(X_minority)
        
        # 生成合成样本
        X_synthetic = []
        for i, num_samples in enumerate(num_samples_to_generate):
            if num_samples > 0:
                # 获取当前样本的k个最近邻（不包括自身）
                neighbors = indices[i, 1:]
                # 随机选择num_samples个邻居
                selected_neighbors = np.random.choice(neighbors, num_samples)
                
                for neighbor_idx in selected_neighbors:
                    # 在当前样本和选定邻居之间生成一个随机点
                    alpha = np.random.random()
                    synthetic_sample = X_minority[i] + alpha * (X_minority[neighbor_idx] - X_minority[i])
                    X_synthetic.append(synthetic_sample)
        
        if len(X_synthetic) > 0:
            X_synthetic = np.array(X_synthetic)
            y_synthetic = np.full(len(X_synthetic), self.minority_class)
            return X_synthetic, y_synthetic
        else:
            return np.empty((0, X.shape[1])), np.array([])
    
    def calculate_sample_weights(self, X, y):
        """
        计算样本权重
        
        参数:
            X (np.ndarray): 特征矩阵
            y (np.ndarray): 标签向量
            
        返回:
            sample_weights (np.ndarray): 样本权重
        """
        # 计算类中心
        minority_indices = np.where(y == self.minority_class)[0]
        majority_indices = np.where(y == self.majority_class)[0]
        
        minority_center = np.mean(X[minority_indices], axis=0)
        majority_center = np.mean(X[majority_indices], axis=0)
        
        # 计算类半径
        minority_radius = np.max(np.linalg.norm(X[minority_indices] - minority_center, axis=1))
        majority_radius = np.max(np.linalg.norm(X[majority_indices] - majority_center, axis=1))
        
        # 初始化样本权重
        sample_weights = np.ones(len(y))
        
        # 计算少数类样本权重
        for i in minority_indices:
            distance = np.linalg.norm(X[i] - minority_center)
            sample_weights[i] = 1 - (distance / (minority_radius + self.delta))
            
        # 计算多数类样本权重
        for i in majority_indices:
            distance = np.linalg.norm(X[i] - majority_center)
            sample_weights[i] = 1 - (distance / (majority_radius + self.delta))
            
        return sample_weights
