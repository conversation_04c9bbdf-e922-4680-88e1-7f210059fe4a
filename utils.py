"""
工具模块
包含各种辅助函数
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import os
import pickle
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA


def save_model(model, filename):
    """
    保存模型到文件
    
    参数:
        model: 要保存的模型
        filename (str): 文件名
    """
    with open(filename, 'wb') as f:
        pickle.dump(model, f)
    print(f"模型已保存到 {filename}")


def load_model(filename):
    """
    从文件加载模型
    
    参数:
        filename (str): 文件名
        
    返回:
        model: 加载的模型
    """
    with open(filename, 'rb') as f:
        model = pickle.load(f)
    print(f"模型已从 {filename} 加载")
    return model


def visualize_data(X, y, title='数据分布可视化', save_path=None):
    """
    使用t-SNE或PCA可视化数据分布
    
    参数:
        X (np.ndarray): 特征矩阵
        y (np.ndarray): 标签向量
        title (str): 图表标题
        save_path (str): 保存路径，如果为None则显示图表
    """
    # 如果特征维度大于2，使用降维
    if X.shape[1] > 2:
        # 尝试使用t-SNE
        try:
            tsne = TSNE(n_components=2, random_state=42)
            X_embedded = tsne.fit_transform(X)
            method = 't-SNE'
        except:
            # 如果t-SNE失败，使用PCA
            pca = PCA(n_components=2, random_state=42)
            X_embedded = pca.fit_transform(X)
            method = 'PCA'
    else:
        X_embedded = X
        method = '原始特征'
    
    # 绘制散点图
    plt.figure(figsize=(10, 8))
    
    # 获取唯一类别
    unique_classes = np.unique(y)
    colors = plt.cm.tab10(np.linspace(0, 1, len(unique_classes)))
    
    for i, cls in enumerate(unique_classes):
        plt.scatter(
            X_embedded[y == cls, 0],
            X_embedded[y == cls, 1],
            c=[colors[i]],
            label=f'类别 {cls}',
            alpha=0.7
        )
    
    plt.title(f'{title} ({method})')
    plt.xlabel('特征1')
    plt.ylabel('特征2')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到 {save_path}")
    else:
        plt.show()


def plot_learning_curve(train_scores, val_scores, title='学习曲线', save_path=None):
    """
    绘制学习曲线
    
    参数:
        train_scores (list): 训练集分数
        val_scores (list): 验证集分数
        title (str): 图表标题
        save_path (str): 保存路径，如果为None则显示图表
    """
    plt.figure(figsize=(10, 6))
    epochs = range(1, len(train_scores) + 1)
    
    plt.plot(epochs, train_scores, 'b-', label='训练集')
    plt.plot(epochs, val_scores, 'r-', label='验证集')
    
    plt.title(title)
    plt.xlabel('轮次')
    plt.ylabel('分数')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到 {save_path}")
    else:
        plt.show()


def plot_confusion_matrix(cm, class_names, title='混淆矩阵', save_path=None):
    """
    绘制混淆矩阵
    
    参数:
        cm (np.ndarray): 混淆矩阵
        class_names (list): 类别名称
        title (str): 图表标题
        save_path (str): 保存路径，如果为None则显示图表
    """
    import seaborn as sns
    
    plt.figure(figsize=(8, 6))
    sns.heatmap(
        cm,
        annot=True,
        fmt='d',
        cmap='Blues',
        xticklabels=class_names,
        yticklabels=class_names
    )
    
    plt.title(title)
    plt.xlabel('预测类别')
    plt.ylabel('真实类别')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到 {save_path}")
    else:
        plt.show()


def compare_models(model_names, metrics_list, title='模型性能比较', save_path=None):
    """
    比较多个模型的性能
    
    参数:
        model_names (list): 模型名称列表
        metrics_list (list): 模型指标列表
        title (str): 图表标题
        save_path (str): 保存路径，如果为None则显示图表
    """
    # 提取要比较的指标
    metrics_to_compare = ['accuracy', 'precision', 'recall', 'f1_score', 'g_mean', 'auc']
    
    # 创建DataFrame
    df = pd.DataFrame(index=model_names)
    
    for metric in metrics_to_compare:
        df[metric] = [metrics[metric] for metrics in metrics_list]
    
    # 绘制条形图
    df.plot(kind='bar', figsize=(12, 8), rot=0)
    
    plt.title(title)
    plt.xlabel('模型')
    plt.ylabel('分数')
    plt.legend(title='评估指标')
    plt.grid(True, axis='y', linestyle='--', alpha=0.7)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到 {save_path}")
    else:
        plt.show()
    
    # 打印详细指标
    print("\n详细指标:")
    print(df.to_string())
    
    return df


def ensure_dir(directory):
    """
    确保目录存在，如果不存在则创建
    
    参数:
        directory (str): 目录路径
    """
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"创建目录: {directory}")


def timer(func):
    """
    计时器装饰器
    
    参数:
        func: 要计时的函数
        
    返回:
        wrapper: 包装后的函数
    """
    import time
    
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"{func.__name__} 执行时间: {end_time - start_time:.2f} 秒")
        return result
    
    return wrapper
