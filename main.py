"""
GA优化ADASYN参数的WGAN-SVM混合框架
主程序入口
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
import argparse
import time
from sklearn.svm import SVC

# 设置环境变量，避免joblib警告
os.environ["LOKY_MAX_CPU_COUNT"] = "4"  # 设置为您想使用的核心数量

# 导入自定义模块
from data_preprocessing import (
    load_dataset, normalize_data, split_data,
    get_class_distribution, get_class_indices
)
from adasyn import ParameterizedADASYN
from wgan import WGAN
from svm_model import CostSensitiveSVM
from genetic_algorithm import GeneticOptimizer
from evaluation import (
    evaluate_model, cross_validation_evaluation,
    calculate_f1_score, calculate_g_mean, calculate_auc
)
from utils import (
    save_model, load_model, visualize_data,
    plot_learning_curve, plot_confusion_matrix,
    compare_models, ensure_dir, timer
)


def parse_args():
    """
    解析命令行参数

    返回:
        args: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='GA优化ADASYN参数的WGAN-SVM混合框架')

    # 数据集参数
    parser.add_argument('--dataset', type=str, default='creditcard', help='数据集名称')
    parser.add_argument('--test_size', type=float, default=0.2, help='测试集比例')#0.2
    parser.add_argument('--random_state', type=int, default=42, help='随机种子')

    # 遗传算法参数
    parser.add_argument('--ga_pop_size', type=int, default=20, help='GA种群大小')#10
    parser.add_argument('--ga_n_generations', type=int, default=20, help='GA迭代代数')#5
    parser.add_argument('--ga_crossover_rate', type=float, default=0.9, help='GA交叉概率')#0.9
    parser.add_argument('--ga_mutation_rate', type=float, default=0.25, help='GA变异概率')#0.2
    parser.add_argument('--ga_elite_size', type=int, default=4, help='GA精英数量')

    # WGAN参数
    parser.add_argument('--wgan_latent_dim', type=int, default=30, help='WGAN潜在空间维度')#30
    parser.add_argument('--wgan_n_critic', type=int, default=10, help='WGAN判别器训练次数')#3
    parser.add_argument('--wgan_clip_value', type=float, default=0.01, help='WGAN权重裁剪值')
    parser.add_argument('--wgan_learning_rate', type=float, default=0.001, help='WGAN学习率')#0.0001
    parser.add_argument('--wgan_batch_size', type=int, default=32, help='WGAN批量大小')#32
    parser.add_argument('--wgan_epochs', type=int, default=300, help='WGAN训练轮数')#100

    # 交叉验证参数
    parser.add_argument('--n_folds', type=int, default=10, help='交叉验证折数')#3

    # 输出参数
    parser.add_argument('--output_dir', type=str, default='results/creditcard', help='输出目录')
    parser.add_argument('--save_model', action='store_true', help='是否保存模型')
    parser.add_argument('--visualize', action='store_true', help='是否可视化数据')

    # 运行模式
    parser.add_argument('--mode', type=str, default='full',
                        choices=['full', 'ga_only', 'wgan_only', 'compare'],
                        help='运行模式')

    args = parser.parse_args()
    return args


@timer
def run_ga_optimization(X_train, y_train, args):
    """
    运行遗传算法优化ADASYN参数

    参数:
        X_train (np.ndarray): 训练特征矩阵
        y_train (np.ndarray): 训练标签向量
        args: 命令行参数

    返回:
        best_params (dict): 最优参数
        best_fitness (float): 最优适应度
    """
    print("\n=== 遗传算法优化ADASYN参数 ===")

    # 初始化遗传算法优化器
    ga_optimizer = GeneticOptimizer(
        pop_size=args.ga_pop_size,
        n_generations=args.ga_n_generations,
        crossover_rate=args.ga_crossover_rate,
        mutation_rate=args.ga_mutation_rate,
        elite_size=args.ga_elite_size,
        random_state=args.random_state
    )

    # 运行优化
    best_params, best_fitness = ga_optimizer.optimize(X_train, y_train, n_folds=args.n_folds)

    print(f"\n最优参数: {best_params}")
    print(f"最优适应度: {best_fitness:.4f}")

    return best_params, best_fitness


@timer
def run_adasyn_sampling(X_train, y_train, params, args):
    """
    使用ADASYN生成合成样本

    参数:
        X_train (np.ndarray): 训练特征矩阵
        y_train (np.ndarray): 训练标签向量
        params (dict): ADASYN参数
        args: 命令行参数

    返回:
        X_resampled (np.ndarray): 重采样后的特征矩阵
        y_resampled (np.ndarray): 重采样后的标签向量
        sample_weights (np.ndarray): 样本权重
    """
    print("\n=== ADASYN生成合成样本 ===")

    # 初始化ADASYN
    adasyn = ParameterizedADASYN(
        beta=params['beta'],
        k=params['k'],
        delta=params['delta'],
        random_state=args.random_state
    )

    # 生成合成样本
    X_resampled, y_resampled = adasyn.fit_resample(X_train, y_train)

    # 计算样本权重
    sample_weights = adasyn.calculate_sample_weights(X_resampled, y_resampled)

    # 获取类别分布
    before_counts, before_ir = get_class_distribution(y_train)
    after_counts, after_ir = get_class_distribution(y_resampled)

    print(f"原始数据集类别分布: {before_counts}, 不平衡比例: {before_ir:.2f}")
    print(f"重采样后类别分布: {after_counts}, 不平衡比例: {after_ir:.2f}")
    print(f"生成了 {len(y_resampled) - len(y_train)} 个合成样本")

    return X_resampled, y_resampled, sample_weights


@timer
def run_wgan_training(X_resampled, y_resampled, args):
    """
    训练WGAN生成高质量样本

    参数:
        X_resampled (np.ndarray): 重采样后的特征矩阵
        y_resampled (np.ndarray): 重采样后的标签向量
        args: 命令行参数

    返回:
        X_wgan (np.ndarray): WGAN生成的高质量样本
        y_wgan (np.ndarray): WGAN生成的样本标签
    """
    print("\n=== 训练WGAN生成高质量样本 ===")

    # 获取少数类样本
    minority_class = min(get_class_distribution(y_resampled)[0], key=get_class_distribution(y_resampled)[0].get)
    minority_indices = np.where(y_resampled == minority_class)[0]
    X_minority = X_resampled[minority_indices]

    # 获取ADASYN生成的少数类样本
    # 假设原始数据集中少数类样本数量
    original_minority_count = np.sum(y_resampled == minority_class) - np.sum(y_resampled[len(y_resampled)-len(X_minority):] == minority_class)
    # ADASYN生成的样本就是重采样后多出来的部分
    adasyn_samples = X_minority[original_minority_count:]

    print(f"使用 {len(adasyn_samples)} 个ADASYN生成的样本作为WGAN生成器的输入")

    # 初始化WGAN
    wgan = WGAN(
        input_dim=X_resampled.shape[1],
        latent_dim=args.wgan_latent_dim,
        clip_value=args.wgan_clip_value,
        n_critic=args.wgan_n_critic,
        learning_rate=args.wgan_learning_rate
    )

    # 使用ADASYN样本训练WGAN
    history = wgan.train_with_adasyn_samples(
        X_minority[:original_minority_count],  # 使用原始少数类样本作为真实样本
        adasyn_samples,  # 使用ADASYN生成的样本作为生成器输入
        batch_size=min(args.wgan_batch_size, len(X_minority)),
        epochs=args.wgan_epochs,
        verbose=1
    )

    # 使用WGAN生成高质量样本
    n_samples_to_generate = len(adasyn_samples)
    X_wgan = wgan.generate_samples_from_adasyn(adasyn_samples)
    y_wgan = np.full(n_samples_to_generate, minority_class)

    print(f"WGAN生成了 {len(X_wgan)} 个高质量样本")

    # 可视化训练过程
    if args.visualize:
        plt.figure(figsize=(12, 5))

        plt.subplot(1, 2, 1)
        plt.plot(history['critic_loss'])
        plt.title('判别器损失')
        plt.xlabel('轮次')
        plt.ylabel('损失')

        plt.subplot(1, 2, 2)
        plt.plot(history['generator_loss'])
        plt.title('生成器损失')
        plt.xlabel('轮次')
        plt.ylabel('损失')

        plt.tight_layout()

        # 保存图表
        ensure_dir(args.output_dir)
        plt.savefig(os.path.join(args.output_dir, 'wgan_training.png'))
        plt.close()

        # 可视化原始样本、ADASYN样本和WGAN样本的对比
        if X_minority.shape[1] > 2:
            from sklearn.decomposition import PCA
            pca = PCA(n_components=2)
            X_combined = np.vstack([X_minority[:original_minority_count], adasyn_samples, X_wgan])
            X_pca = pca.fit_transform(X_combined)

            plt.figure(figsize=(10, 8))
            plt.scatter(X_pca[:original_minority_count, 0], X_pca[:original_minority_count, 1],
                      c='blue', label='原始少数类样本', alpha=0.7)
            plt.scatter(X_pca[original_minority_count:original_minority_count+len(adasyn_samples), 0],
                      X_pca[original_minority_count:original_minority_count+len(adasyn_samples), 1],
                      c='green', label='ADASYN生成的样本', alpha=0.7)
            plt.scatter(X_pca[original_minority_count+len(adasyn_samples):, 0],
                      X_pca[original_minority_count+len(adasyn_samples):, 1],
                      c='red', label='WGAN生成的样本', alpha=0.7)
            plt.title('原始样本、ADASYN样本和WGAN样本的对比')
            plt.legend()
            plt.savefig(os.path.join(args.output_dir, 'samples_comparison.png'))
            plt.close()

    return X_wgan, y_wgan


@timer
def train_cost_sensitive_svm(X_train, y_train, X_wgan, y_wgan, sample_weights, params, args):
    """
    训练代价敏感SVM模型

    参数:
        X_train (np.ndarray): 训练特征矩阵
        y_train (np.ndarray): 训练标签向量
        X_wgan (np.ndarray): WGAN生成的高质量样本
        y_wgan (np.ndarray): WGAN生成的样本标签
        sample_weights (np.ndarray): 样本权重
        params (dict): 模型参数
        args: 命令行参数

    返回:
        model: 训练好的模型
    """
    print("\n=== 训练代价敏感SVM模型 ===")

    # 合并原始样本和WGAN生成的样本
    X_combined = np.vstack((X_train, X_wgan))
    y_combined = np.hstack((y_train, y_wgan))

    # 扩展样本权重
    wgan_weights = np.ones(len(y_wgan)) * 0.8  # WGAN样本权重略低
    combined_weights = np.hstack((sample_weights, wgan_weights))

    # 初始化代价敏感SVM
    model = CostSensitiveSVM(
        C_pos=params['C_pos'],
        C_neg=params['C_neg'],
        delta=params['delta'],
        kernel='rbf',
        gamma='scale',
        probability=True,
        random_state=args.random_state
    )

    # 训练模型
    model.fit(X_combined, y_combined, sample_weight=combined_weights)

    print("代价敏感SVM模型训练完成")

    # 保存模型
    if args.save_model:
        ensure_dir(args.output_dir)
        save_model(model, os.path.join(args.output_dir, 'cs_svm_model.pkl'))

    return model


@timer
def evaluate_and_compare(X_test, y_test, model, args):
    """
    评估模型并与基准方法比较

    参数:
        X_test (np.ndarray): 测试特征矩阵
        y_test (np.ndarray): 测试标签向量
        model: 训练好的模型
        args: 命令行参数

    返回:
        comparison_df (pd.DataFrame): 比较结果
    """
    print("\n=== 评估模型并与基准方法比较 ===")

    # 评估提出的模型
    proposed_metrics = evaluate_model(model, X_test, y_test)

    print("\n提出的模型 (GA-ADASYN-WGAN-SVM) 性能:")
    for metric, value in proposed_metrics.items():
        if metric != 'confusion_matrix':
            print(f"{metric}: {value:.4f}")

    # 如果是比较模式，训练和评估基准方法
    if args.mode == 'compare':
        # 初始化基准方法
        baseline_models = {
            'SVM': SVC(probability=True, random_state=args.random_state),
            'ADASYN-SVM': None,
            'WGAN-SVM': None
        }

        # 加载原始训练数据
        X, y = load_dataset(args.dataset)
        X, _ = normalize_data(X)
        X_train, X_test, y_train, y_test = split_data(X, y, test_size=args.test_size, random_state=args.random_state)

        # 训练和评估原始SVM
        baseline_models['SVM'].fit(X_train, y_train)
        svm_metrics = evaluate_model(baseline_models['SVM'], X_test, y_test)

        # 训练和评估ADASYN-SVM
        adasyn = ParameterizedADASYN(random_state=args.random_state)
        X_resampled, y_resampled = adasyn.fit_resample(X_train, y_train)

        baseline_models['ADASYN-SVM'] = SVC(probability=True, random_state=args.random_state)
        baseline_models['ADASYN-SVM'].fit(X_resampled, y_resampled)
        adasyn_svm_metrics = evaluate_model(baseline_models['ADASYN-SVM'], X_test, y_test)

        # 训练和评估WGAN-SVM
        # 获取少数类样本
        minority_class = min(get_class_distribution(y_train)[0], key=get_class_distribution(y_train)[0].get)
        minority_indices = np.where(y_train == minority_class)[0]
        X_minority = X_train[minority_indices]

        # 训练WGAN
        wgan = WGAN(
            input_dim=X_train.shape[1],
            latent_dim=args.wgan_latent_dim,
            random_state=args.random_state
        )

        wgan.train(
            X_minority,
            batch_size=min(args.wgan_batch_size, len(X_minority)),
            epochs=args.wgan_epochs // 2,  # 减少训练轮数以加快比较
            verbose=0
        )

        # 生成样本
        n_samples_to_generate = len(X_train) - len(X_minority)
        X_wgan = wgan.generate_samples(n_samples_to_generate)
        y_wgan = np.full(n_samples_to_generate, minority_class)

        # 合并原始样本和WGAN生成的样本
        X_wgan_combined = np.vstack((X_train, X_wgan))
        y_wgan_combined = np.hstack((y_train, y_wgan))

        # 训练WGAN-SVM
        baseline_models['WGAN-SVM'] = SVC(probability=True, random_state=args.random_state)
        baseline_models['WGAN-SVM'].fit(X_wgan_combined, y_wgan_combined)
        wgan_svm_metrics = evaluate_model(baseline_models['WGAN-SVM'], X_test, y_test)

        # 比较所有方法
        model_names = ['SVM', 'ADASYN-SVM', 'WGAN-SVM', 'GA-ADASYN-WGAN-SVM']
        metrics_list = [svm_metrics, adasyn_svm_metrics, wgan_svm_metrics, proposed_metrics]

        comparison_df = compare_models(
            model_names,
            metrics_list,
            title='模型性能比较',
            save_path=os.path.join(args.output_dir, 'model_comparison.png')
        )

        return comparison_df

    return None


def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()

    # 确保输出目录存在
    ensure_dir(args.output_dir)

    print(f"\n=== GA优化ADASYN参数的WGAN-SVM混合框架 ===")
    print(f"数据集: {args.dataset}")
    print(f"运行模式: {args.mode}")

    # 加载数据集
    X, y = load_dataset(args.dataset)
    if X is None or y is None:
        print(f"无法加载数据集 {args.dataset}，请检查数据集名称或路径")
        return

    # 数据预处理
    X, scaler = normalize_data(X)
    X_train, X_test, y_train, y_test = split_data(X, y, test_size=args.test_size, random_state=args.random_state)

    # 获取类别分布
    class_counts, imbalance_ratio = get_class_distribution(y)
    print(f"\n数据集类别分布: {class_counts}")
    print(f"不平衡比例: {imbalance_ratio:.2f}")

    # 可视化原始数据
    if args.visualize:
        visualize_data(
            X, y,
            title=f'{args.dataset} 原始数据分布',
            save_path=os.path.join(args.output_dir, 'original_data.png')
        )

    # 根据运行模式执行不同的流程
    if args.mode in ['full', 'ga_only']:
        # 遗传算法优化ADASYN参数
        best_params, _ = run_ga_optimization(X_train, y_train, args)
    else:
        # 使用默认参数
        best_params = {
            'beta': 0.5,
            'k': 5,
            'delta': 0.1,
            'C_pos': 1.0,
            'C_neg': 1.0
        }

    # ADASYN生成合成样本
    X_resampled, y_resampled, sample_weights = run_adasyn_sampling(X_train, y_train, best_params, args)

    # 可视化重采样后的数据
    if args.visualize:
        visualize_data(
            X_resampled, y_resampled,
            title=f'{args.dataset} ADASYN重采样后数据分布',
            save_path=os.path.join(args.output_dir, 'adasyn_resampled_data.png')
        )

    if args.mode in ['full', 'wgan_only']:
        # 训练WGAN生成高质量样本
        X_wgan, y_wgan = run_wgan_training(X_resampled, y_resampled, args)

        # 可视化WGAN生成的样本
        if args.visualize:
            # 获取少数类
            minority_class = min(get_class_distribution(y_resampled)[0], key=get_class_distribution(y_resampled)[0].get)
            minority_indices = np.where(y_resampled == minority_class)[0]
            X_minority = X_resampled[minority_indices]

            # 合并少数类样本和WGAN生成的样本
            X_combined = np.vstack((X_minority, X_wgan))
            y_combined = np.hstack((np.full(len(X_minority), minority_class), y_wgan))

            visualize_data(
                X_combined, y_combined,
                title=f'{args.dataset} 少数类与WGAN生成样本对比',
                save_path=os.path.join(args.output_dir, 'wgan_generated_data.png')
            )
    else:
        # 不使用WGAN，直接使用ADASYN样本
        X_wgan = np.empty((0, X_resampled.shape[1]))
        y_wgan = np.array([])

    # 训练代价敏感SVM
    model = train_cost_sensitive_svm(X_resampled, y_resampled, X_wgan, y_wgan, sample_weights, best_params, args)

    # 评估模型并与基准方法比较
    comparison_df = evaluate_and_compare(X_test, y_test, model, args)

    print("\n=== 完成 ===")


if __name__ == '__main__':
    start_time = time.time()
    main()
    end_time = time.time()
    print(f"\n总运行时间: {end_time - start_time:.2f} 秒")
