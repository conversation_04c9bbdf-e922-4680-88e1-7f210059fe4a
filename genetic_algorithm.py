"""
遗传算法优化模块
用于优化ADASYN参数和SVM惩罚因子
"""

import numpy as np
import random
from sklearn.model_selection import StratifiedKFold
from adasyn import ParameterizedADASYN
from svm_model import CostSensitiveSVM
from evaluation import calculate_f1_score, calculate_g_mean, calculate_auc


class GeneticOptimizer:
    """
    遗传算法优化器

    参数:
        pop_size (int): 种群大小
        n_generations (int): 迭代代数
        crossover_rate (float): 交叉概率
        mutation_rate (float): 变异概率
        elite_size (int): 精英数量
        random_state (int): 随机种子
    """

    def __init__(self, pop_size=50, n_generations=30, crossover_rate=0.8,# 50 30 0.8
                 mutation_rate=0.1, elite_size=5, random_state=None):
        self.pop_size = pop_size
        self.n_generations = n_generations
        self.crossover_rate = crossover_rate
        self.mutation_rate = mutation_rate
        self.elite_size = elite_size
        self.random_state = random_state

        if random_state is not None:
            np.random.seed(random_state)
            random.seed(random_state)

    def optimize(self, X, y, n_folds=5):
        """
        使用遗传算法优化ADASYN参数和SVM惩罚因子

        参数:
            X (np.ndarray): 特征矩阵
            y (np.ndarray): 标签向量
            n_folds (int): 交叉验证折数

        返回:
            best_params (dict): 最优参数
            best_fitness (float): 最优适应度
        """
        # 初始化种群，传入数据集信息以确定k的上限
        population = self._initialize_population(X, y)

        # 评估初始种群
        fitness_scores = self._evaluate_population(population, X, y, n_folds)

        # 迭代优化
        for generation in range(self.n_generations):
            # 选择精英个体
            elites = self._select_elites(population, fitness_scores)

            # 选择父代
            parents = self._select_parents(population, fitness_scores)

            # 交叉生成子代
            offspring = self._crossover(parents)

            # 变异
            offspring = self._mutate(offspring, X, y)

            # 更新种群
            population = elites + offspring

            # 评估新种群
            fitness_scores = self._evaluate_population(population, X, y, n_folds)

            # 打印当前代最优适应度
            best_idx = np.argmax(fitness_scores)
            best_fitness = fitness_scores[best_idx]
            print(f"Generation {generation+1}/{self.n_generations}, Best Fitness: {best_fitness:.4f}")

        # 获取最优参数
        best_idx = np.argmax(fitness_scores)
        best_chromosome = population[best_idx]
        best_params = self._decode_chromosome(best_chromosome)
        best_fitness = fitness_scores[best_idx]

        return best_params, best_fitness

    def _initialize_population(self, X=None, y=None):
        """
        初始化种群

        参数:
            X (np.ndarray, optional): 特征矩阵，用于确定k的上限
            y (np.ndarray, optional): 标签向量，用于确定少数类样本数量

        返回:
            population (list): 初始种群
        """
        population = []

        # 确定k的上限
        k_max = 15  # 默认值
        if X is not None and y is not None:
            # 获取少数类样本数量
            class_counts = np.bincount(y.astype(int))
            min_class_count = np.min(class_counts[class_counts > 0])
            # k不能超过少数类样本数量-1（因为kNN算法中包括样本本身）
            k_max = min(min_class_count - 1, 15)
            # 确保k至少为2
            k_max = max(k_max, 2)

        for _ in range(self.pop_size):
            # 生成染色体
            chromosome = {
                'beta': np.random.uniform(0.1, 1.0),
                'k': np.random.randint(2, k_max + 1),
                'delta': np.random.uniform(0.01, 0.5),
                'C_pos': np.random.uniform(0.1, 10.0),
                'C_neg': np.random.uniform(0.1, 10.0)
            }

            population.append(chromosome)

        return population

    def _evaluate_population(self, population, X, y, n_folds):
        """
        评估种群中每个个体的适应度

        参数:
            population (list): 种群
            X (np.ndarray): 特征矩阵
            y (np.ndarray): 标签向量
            n_folds (int): 交叉验证折数

        返回:
            fitness_scores (np.ndarray): 适应度分数
        """
        fitness_scores = np.zeros(len(population))

        # 使用交叉验证评估每个个体
        skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=self.random_state)

        for i, chromosome in enumerate(population):
            # 解码染色体
            params = self._decode_chromosome(chromosome)

            # 初始化性能指标
            f1_scores = []
            g_means = []
            auc_scores = []

            # 交叉验证
            for train_idx, val_idx in skf.split(X, y):
                X_train, X_val = X[train_idx], X[val_idx]
                y_train, y_val = y[train_idx], y[val_idx]

                # 使用ADASYN生成合成样本
                adasyn = ParameterizedADASYN(
                    beta=params['beta'],
                    k=params['k'],
                    delta=params['delta'],
                    random_state=self.random_state
                )

                X_resampled, y_resampled = adasyn.fit_resample(X_train, y_train)

                # 计算样本权重
                sample_weights = adasyn.calculate_sample_weights(X_resampled, y_resampled)

                # 训练代价敏感SVM
                svm = CostSensitiveSVM(
                    C_pos=params['C_pos'],
                    C_neg=params['C_neg'],
                    delta=params['delta'],
                    random_state=self.random_state
                )

                svm.fit(X_resampled, y_resampled, sample_weight=sample_weights)

                # 在验证集上评估
                y_pred = svm.predict(X_val)
                y_prob = svm.predict_proba(X_val)[:, 1] if len(np.unique(y)) == 2 else None

                # 计算性能指标
                f1 = calculate_f1_score(y_val, y_pred)
                g_mean = calculate_g_mean(y_val, y_pred)
                auc = calculate_auc(y_val, y_prob) if y_prob is not None else 0.5

                f1_scores.append(f1)
                g_means.append(g_mean)
                auc_scores.append(auc)

            # 计算平均性能指标
            avg_f1 = np.mean(f1_scores)
            avg_g_mean = np.mean(g_means)
            avg_auc = np.mean(auc_scores)

            # 计算适应度（加权平均）
            fitness = 0.4 * avg_f1 + 0.4 * avg_g_mean + 0.2 * avg_auc
            fitness_scores[i] = fitness

        return fitness_scores

    def _select_elites(self, population, fitness_scores):
        """
        选择精英个体

        参数:
            population (list): 种群
            fitness_scores (np.ndarray): 适应度分数

        返回:
            elites (list): 精英个体
        """
        # 获取适应度排序索引
        sorted_indices = np.argsort(fitness_scores)[::-1]

        # 选择前elite_size个个体
        elite_indices = sorted_indices[:self.elite_size]
        elites = [population[i] for i in elite_indices]

        return elites

    def _select_parents(self, population, fitness_scores):
        """
        使用轮盘赌选择父代

        参数:
            population (list): 种群
            fitness_scores (np.ndarray): 适应度分数

        返回:
            parents (list): 选择的父代
        """
        # 计算选择概率
        total_fitness = np.sum(fitness_scores)
        selection_probs = fitness_scores / total_fitness if total_fitness > 0 else np.ones_like(fitness_scores) / len(fitness_scores)

        # 选择父代
        n_parents = self.pop_size - self.elite_size
        parents = []

        for _ in range(n_parents):
            # 轮盘赌选择
            idx = np.random.choice(len(population), p=selection_probs)
            parents.append(population[idx])

        return parents

    def _crossover(self, parents):
        """
        交叉操作生成子代

        参数:
            parents (list): 父代

        返回:
            offspring (list): 子代
        """
        offspring = []
        n_parents = len(parents)

        for i in range(0, n_parents, 2):
            parent1 = parents[i]
            parent2 = parents[i+1] if i+1 < n_parents else parents[0]

            # 以一定概率进行交叉
            if np.random.random() < self.crossover_rate:
                # 算术交叉
                child1 = {}
                child2 = {}

                for key in parent1.keys():
                    # 生成随机权重
                    alpha = np.random.random()

                    # 交叉
                    child1[key] = alpha * parent1[key] + (1 - alpha) * parent2[key]
                    child2[key] = alpha * parent2[key] + (1 - alpha) * parent1[key]

                offspring.append(child1)
                if len(offspring) < n_parents:
                    offspring.append(child2)
            else:
                # 不交叉，直接复制父代
                offspring.append(parent1.copy())
                if len(offspring) < n_parents:
                    offspring.append(parent2.copy())

        return offspring

    def _mutate(self, offspring, X=None, y=None):
        """
        变异操作

        参数:
            offspring (list): 子代
            X (np.ndarray, optional): 特征矩阵，用于确定k的上限
            y (np.ndarray, optional): 标签向量，用于确定少数类样本数量

        返回:
            mutated_offspring (list): 变异后的子代
        """
        mutated_offspring = []

        # 确定k的上限
        k_max = 15  # 默认值
        if X is not None and y is not None:
            # 获取少数类样本数量
            class_counts = np.bincount(y.astype(int))
            min_class_count = np.min(class_counts[class_counts > 0])
            # k不能超过少数类样本数量-1
            k_max = min(min_class_count - 1, 15)
            # 确保k至少为2
            k_max = max(k_max, 2)

        for chromosome in offspring:
            # 复制染色体
            mutated_chromosome = chromosome.copy()

            # 对每个基因以一定概率进行变异
            for key in mutated_chromosome.keys():
                if np.random.random() < self.mutation_rate:
                    if key == 'beta':
                        # beta变异
                        mutated_chromosome[key] = np.random.uniform(0.1, 1.0)
                    elif key == 'k':
                        # k变异
                        mutated_chromosome[key] = np.random.randint(2, k_max + 1)
                    elif key == 'delta':
                        # delta变异
                        mutated_chromosome[key] = np.random.uniform(0.01, 0.5)
                    elif key in ['C_pos', 'C_neg']:
                        # C变异
                        mutated_chromosome[key] = np.random.uniform(0.1, 10.0)

            mutated_offspring.append(mutated_chromosome)

        return mutated_offspring

    def _decode_chromosome(self, chromosome):
        """
        解码染色体

        参数:
            chromosome (dict): 染色体

        返回:
            params (dict): 解码后的参数
        """
        params = {
            'beta': chromosome['beta'],
            'k': int(chromosome['k']),
            'delta': chromosome['delta'],
            'C_pos': chromosome['C_pos'],
            'C_neg': chromosome['C_neg']
        }

        return params
