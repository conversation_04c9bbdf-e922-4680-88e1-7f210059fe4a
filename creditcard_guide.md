# 信用卡欺诈检测 - GA优化ADASYN-WGAN-SVM框架使用指南

## 简介

本框架结合了遗传算法(GA)、自适应合成采样(ADASYN)、Wasserstein生成对抗网络(WGAN)和支持向量机(SVM)的优势，专门用于解决信用卡欺诈检测这类高度不平衡的分类问题。

## 数据集准备

1. 从以下来源获取信用卡欺诈检测数据集(creditcard.csv)：
   - Kaggle: https://www.kaggle.com/mlg-ulb/creditcardfraud
   - UCI机器学习仓库

2. 将下载的creditcard.csv文件放在`data`目录下

## 运行方法

### 方法一：使用批处理脚本

1. 双击运行`run_creditcard.bat`
2. 在菜单中选择运行模式：
   - 完整模式：运行完整的GA-ADASYN-WGAN-SVM框架
   - 仅GA优化模式：只运行GA优化ADASYN参数部分
   - 仅WGAN模式：使用默认ADASYN参数，运行WGAN-SVM部分
   - 比较模式：将提出的方法与基准方法(SVM、ADASYN-SVM、WGAN-SVM)进行比较

### 方法二：命令行运行

```bash
# 完整模式
python main.py --dataset creditcard --mode full --visualize

# 仅GA优化模式
python main.py --dataset creditcard --mode ga_only --visualize

# 仅WGAN模式
python main.py --dataset creditcard --mode wgan_only --visualize

# 比较模式
python main.py --dataset creditcard --mode compare --visualize
```

## 参数说明

可以通过修改命令行参数来调整框架的行为：

```bash
python main.py --dataset creditcard --ga_pop_size 20 --ga_n_generations 10 --wgan_epochs 200 --mode full --visualize
```

主要参数：
- `--dataset`：数据集名称，默认为'creditcard'
- `--test_size`：测试集比例，默认为0.2
- `--random_state`：随机种子，默认为42

GA参数：
- `--ga_pop_size`：GA种群大小，默认为10
- `--ga_n_generations`：GA迭代代数，默认为5
- `--ga_crossover_rate`：GA交叉概率，默认为0.8
- `--ga_mutation_rate`：GA变异概率，默认为0.2
- `--ga_elite_size`：GA精英数量，默认为2

WGAN参数：
- `--wgan_latent_dim`：WGAN潜在空间维度，默认为30
- `--wgan_n_critic`：WGAN判别器训练次数，默认为3
- `--wgan_clip_value`：WGAN权重裁剪值，默认为0.01
- `--wgan_learning_rate`：WGAN学习率，默认为0.0001
- `--wgan_batch_size`：WGAN批量大小，默认为32
- `--wgan_epochs`：WGAN训练轮数，默认为100

其他参数：
- `--n_folds`：交叉验证折数，默认为3
- `--output_dir`：输出目录，默认为'results/creditcard'
- `--save_model`：是否保存模型，默认为False
- `--visualize`：是否可视化数据，默认为False
- `--mode`：运行模式，可选['full', 'ga_only', 'wgan_only', 'compare']，默认为'full'

## 输出结果

所有结果将保存在`results/creditcard`目录下：

1. **可视化结果**：
   - `original_data.png`：原始数据分布
   - `adasyn_resampled_data.png`：ADASYN重采样后的数据分布
   - `wgan_generated_data.png`：WGAN生成的样本与原始少数类样本对比
   - `wgan_training.png`：WGAN训练过程中的损失曲线
   - `model_comparison.png`：不同模型的性能比较(仅比较模式)

2. **模型文件**：
   - `cs_svm_model.pkl`：训练好的代价敏感SVM模型(如果使用--save_model参数)

## 性能指标

框架使用以下指标评估模型性能：
- 准确率(Accuracy)
- 精确率(Precision)
- 召回率(Recall)
- F1分数(F1-score)
- G-mean
- AUC
- 敏感度(Sensitivity)
- 特异度(Specificity)

## 注意事项

1. 由于信用卡欺诈检测数据集较大，程序会自动选择所有欺诈样本和部分非欺诈样本进行处理，以加快训练速度。

2. 如果遇到内存不足的问题，可以尝试减小参数值：
   - 减小GA种群大小(`--ga_pop_size`)
   - 减小WGAN批量大小(`--wgan_batch_size`)
   - 减小WGAN潜在空间维度(`--wgan_latent_dim`)

3. 如果遇到运行时间过长的问题，可以尝试：
   - 减少GA迭代代数(`--ga_n_generations`)
   - 减少WGAN训练轮数(`--wgan_epochs`)
   - 减少交叉验证折数(`--n_folds`)

4. 如果出现"joblib警告"，可以忽略，这不会影响程序的正常运行。
