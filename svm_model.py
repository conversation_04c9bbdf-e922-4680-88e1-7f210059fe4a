"""
代价敏感SVM模型模块
实现带权重的代价敏感SVM
"""

import numpy as np
from sklearn.svm import SVC
from sklearn.base import BaseEstimator, ClassifierMixin


class CostSensitiveSVM(BaseEstimator, ClassifierMixin):
    """
    代价敏感SVM模型
    
    参数:
        C_pos (float): 正类（少数类）的惩罚因子
        C_neg (float): 负类（多数类）的惩罚因子
        kernel (str): 核函数类型
        delta (float): 权值计算参数
        **kwargs: 其他传递给SVC的参数
    """
    
    def __init__(self, C_pos=1.0, C_neg=1.0, kernel='rbf', delta=0.1, **kwargs):
        self.C_pos = C_pos
        self.C_neg = C_neg
        self.kernel = kernel
        self.delta = delta
        self.kwargs = kwargs
        self.svm = None
        self.minority_class = None
        self.majority_class = None
        self.class_weights = None
        
    def fit(self, X, y, sample_weight=None):
        """
        训练代价敏感SVM模型
        
        参数:
            X (np.ndarray): 特征矩阵
            y (np.ndarray): 标签向量
            sample_weight (np.ndarray, optional): 样本权重
            
        返回:
            self: 训练好的模型
        """
        # 确定少数类和多数类
        unique_classes, class_counts = np.unique(y, return_counts=True)
        if len(unique_classes) != 2:
            raise ValueError("只支持二分类问题")
            
        class_count_dict = dict(zip(unique_classes, class_counts))
        self.minority_class = min(class_count_dict, key=class_count_dict.get)
        self.majority_class = max(class_count_dict, key=class_count_dict.get)
        
        # 设置类权重
        self.class_weights = {
            self.minority_class: self.C_pos,
            self.majority_class: self.C_neg
        }
        
        # 如果没有提供样本权重，计算基于距离的权重
        if sample_weight is None:
            sample_weight = self._calculate_sample_weights(X, y)
        
        # 创建并训练SVM模型
        self.svm = SVC(
            kernel=self.kernel,
            class_weight=self.class_weights,
            **self.kwargs
        )
        
        self.svm.fit(X, y, sample_weight=sample_weight)
        
        return self
    
    def predict(self, X):
        """
        使用训练好的模型进行预测
        
        参数:
            X (np.ndarray): 特征矩阵
            
        返回:
            y_pred (np.ndarray): 预测标签
        """
        if self.svm is None:
            raise ValueError("模型尚未训练")
            
        return self.svm.predict(X)
    
    def predict_proba(self, X):
        """
        预测样本属于各类别的概率
        
        参数:
            X (np.ndarray): 特征矩阵
            
        返回:
            probas (np.ndarray): 预测概率
        """
        if self.svm is None:
            raise ValueError("模型尚未训练")
            
        if hasattr(self.svm, 'predict_proba'):
            return self.svm.predict_proba(X)
        else:
            # 如果SVM不支持概率输出，使用决策函数
            decision_values = self.svm.decision_function(X)
            # 将决策值转换为概率
            probas = 1 / (1 + np.exp(-decision_values))
            # 返回两列概率 [1-p, p]
            return np.vstack([1-probas, probas]).T
    
    def _calculate_sample_weights(self, X, y):
        """
        计算基于距离的样本权重
        
        参数:
            X (np.ndarray): 特征矩阵
            y (np.ndarray): 标签向量
            
        返回:
            sample_weights (np.ndarray): 样本权重
        """
        # 获取少数类和多数类样本
        minority_indices = np.where(y == self.minority_class)[0]
        majority_indices = np.where(y == self.majority_class)[0]
        
        # 计算类中心
        minority_center = np.mean(X[minority_indices], axis=0)
        majority_center = np.mean(X[majority_indices], axis=0)
        
        # 计算类半径
        minority_radius = np.max(np.linalg.norm(X[minority_indices] - minority_center, axis=1))
        majority_radius = np.max(np.linalg.norm(X[majority_indices] - majority_center, axis=1))
        
        # 初始化样本权重
        sample_weights = np.ones(len(y))
        
        # 计算少数类样本权重
        for i in minority_indices:
            distance = np.linalg.norm(X[i] - minority_center)
            # 根据公式 w_i = 1 - (||x_± - x_i||)/(r_± + δ)
            sample_weights[i] = self.C_pos * (1 - (distance / (minority_radius + self.delta)))
            
        # 计算多数类样本权重
        for i in majority_indices:
            distance = np.linalg.norm(X[i] - majority_center)
            # 根据公式 w_i = 1 - (||x_± - x_i||)/(r_± + δ)
            sample_weights[i] = self.C_neg * (1 - (distance / (majority_radius + self.delta)))
            
        # 确保权重为正
        sample_weights = np.maximum(sample_weights, 0.01)
        
        return sample_weights
    
    def get_params(self, deep=True):
        """
        获取模型参数
        
        参数:
            deep (bool): 是否返回深层参数
            
        返回:
            params (dict): 模型参数
        """
        params = {
            'C_pos': self.C_pos,
            'C_neg': self.C_neg,
            'kernel': self.kernel,
            'delta': self.delta
        }
        
        if deep:
            params.update(self.kwargs)
            
        return params
    
    def set_params(self, **parameters):
        """
        设置模型参数
        
        参数:
            **parameters: 要设置的参数
            
        返回:
            self: 更新参数后的模型
        """
        for parameter, value in parameters.items():
            if parameter in ['C_pos', 'C_neg', 'kernel', 'delta']:
                setattr(self, parameter, value)
            else:
                self.kwargs[parameter] = value
                
        return self
