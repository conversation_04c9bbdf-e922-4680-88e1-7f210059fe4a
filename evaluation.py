"""
评估模块
实现各种评估指标
"""

import numpy as np
from sklearn.metrics import (
    confusion_matrix, f1_score, precision_score, recall_score,
    roc_auc_score, accuracy_score
)


def calculate_confusion_matrix(y_true, y_pred):
    """
    计算混淆矩阵
    
    参数:
        y_true (np.ndarray): 真实标签
        y_pred (np.ndarray): 预测标签
        
    返回:
        tn (int): 真负例数量
        fp (int): 假正例数量
        fn (int): 假负例数量
        tp (int): 真正例数量
    """
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
    return tn, fp, fn, tp


def calculate_accuracy(y_true, y_pred):
    """
    计算准确率
    
    参数:
        y_true (np.ndarray): 真实标签
        y_pred (np.ndarray): 预测标签
        
    返回:
        accuracy (float): 准确率
    """
    return accuracy_score(y_true, y_pred)


def calculate_precision(y_true, y_pred):
    """
    计算精确率
    
    参数:
        y_true (np.ndarray): 真实标签
        y_pred (np.ndarray): 预测标签
        
    返回:
        precision (float): 精确率
    """
    return precision_score(y_true, y_pred)


def calculate_recall(y_true, y_pred):
    """
    计算召回率
    
    参数:
        y_true (np.ndarray): 真实标签
        y_pred (np.ndarray): 预测标签
        
    返回:
        recall (float): 召回率
    """
    return recall_score(y_true, y_pred)


def calculate_f1_score(y_true, y_pred):
    """
    计算F1分数
    
    参数:
        y_true (np.ndarray): 真实标签
        y_pred (np.ndarray): 预测标签
        
    返回:
        f1 (float): F1分数
    """
    return f1_score(y_true, y_pred)


def calculate_g_mean(y_true, y_pred):
    """
    计算G-mean
    
    参数:
        y_true (np.ndarray): 真实标签
        y_pred (np.ndarray): 预测标签
        
    返回:
        g_mean (float): G-mean
    """
    tn, fp, fn, tp = calculate_confusion_matrix(y_true, y_pred)
    
    # 计算敏感度和特异度
    sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    
    # 计算G-mean
    g_mean = np.sqrt(sensitivity * specificity)
    
    return g_mean


def calculate_auc(y_true, y_prob):
    """
    计算AUC
    
    参数:
        y_true (np.ndarray): 真实标签
        y_prob (np.ndarray): 预测概率
        
    返回:
        auc (float): AUC
    """
    if y_prob is None:
        return 0.5
    
    try:
        return roc_auc_score(y_true, y_prob)
    except:
        return 0.5


def evaluate_model(model, X_test, y_test):
    """
    全面评估模型性能
    
    参数:
        model: 训练好的模型
        X_test (np.ndarray): 测试特征矩阵
        y_test (np.ndarray): 测试标签向量
        
    返回:
        metrics (dict): 评估指标
    """
    # 预测
    y_pred = model.predict(X_test)
    y_prob = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
    
    # 计算混淆矩阵
    tn, fp, fn, tp = calculate_confusion_matrix(y_test, y_pred)
    
    # 计算各项指标
    accuracy = calculate_accuracy(y_test, y_pred)
    precision = calculate_precision(y_test, y_pred)
    recall = calculate_recall(y_test, y_pred)
    f1 = calculate_f1_score(y_test, y_pred)
    g_mean = calculate_g_mean(y_test, y_pred)
    auc = calculate_auc(y_test, y_prob)
    
    # 计算敏感度和特异度
    sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    
    # 汇总指标
    metrics = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'g_mean': g_mean,
        'auc': auc,
        'sensitivity': sensitivity,
        'specificity': specificity,
        'confusion_matrix': {
            'tn': tn,
            'fp': fp,
            'fn': fn,
            'tp': tp
        }
    }
    
    return metrics


def cross_validation_evaluation(model, X, y, cv, random_state=None):
    """
    使用交叉验证评估模型
    
    参数:
        model: 模型
        X (np.ndarray): 特征矩阵
        y (np.ndarray): 标签向量
        cv (int): 交叉验证折数
        random_state (int): 随机种子
        
    返回:
        avg_metrics (dict): 平均评估指标
    """
    from sklearn.model_selection import StratifiedKFold
    
    # 初始化交叉验证
    skf = StratifiedKFold(n_splits=cv, shuffle=True, random_state=random_state)
    
    # 初始化指标列表
    metrics_list = []
    
    # 交叉验证
    for train_idx, test_idx in skf.split(X, y):
        X_train, X_test = X[train_idx], X[test_idx]
        y_train, y_test = y[train_idx], y[test_idx]
        
        # 训练模型
        model.fit(X_train, y_train)
        
        # 评估模型
        metrics = evaluate_model(model, X_test, y_test)
        metrics_list.append(metrics)
    
    # 计算平均指标
    avg_metrics = {}
    for key in metrics_list[0].keys():
        if key != 'confusion_matrix':
            avg_metrics[key] = np.mean([m[key] for m in metrics_list])
    
    # 计算平均混淆矩阵
    avg_metrics['confusion_matrix'] = {
        'tn': np.mean([m['confusion_matrix']['tn'] for m in metrics_list]),
        'fp': np.mean([m['confusion_matrix']['fp'] for m in metrics_list]),
        'fn': np.mean([m['confusion_matrix']['fn'] for m in metrics_list]),
        'tp': np.mean([m['confusion_matrix']['tp'] for m in metrics_list])
    }
    
    return avg_metrics
