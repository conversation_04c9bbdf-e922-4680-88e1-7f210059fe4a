"""
快速WGAN损失函数图生成脚本
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
import time
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from imblearn.over_sampling import ADASYN

# 设置环境变量，避免joblib警告
os.environ["LOKY_MAX_CPU_COUNT"] = "4"

# 导入自定义模块
from data_preprocessing import get_class_distribution
from wgan import WGAN


def load_creditcard_dataset():
    """
    加载信用卡欺诈检测数据集
    """
    print("加载信用卡欺诈检测数据集...")
    try:
        data = pd.read_csv('data/creditcard.csv')
        X = data.drop('Class', axis=1).values
        y = data['Class'].values
        
        # 选择部分数据以加快处理速度
        fraud_indices = np.where(y == 1)[0]
        non_fraud_indices = np.where(y == 0)[0]
        
        n_non_fraud = min(len(non_fraud_indices), len(fraud_indices) * 3)  # 减少数据量
        selected_non_fraud_indices = np.random.choice(non_fraud_indices, n_non_fraud, replace=False)
        
        selected_indices = np.concatenate([fraud_indices, selected_non_fraud_indices])
        X = X[selected_indices]
        y = y[selected_indices]
        
        print(f"加载了 {len(fraud_indices)} 个欺诈样本和 {n_non_fraud} 个非欺诈样本")
        return X, y
    except Exception as e:
        print(f"加载数据集时出错: {e}")
        return None, None


def plot_simple_loss_curves(history, save_path):
    """
    绘制简单的损失曲线图
    
    参数:
        history (dict): 训练历史
        save_path (str): 保存路径
    """
    epochs = range(1, len(history['critic_loss']) + 1)
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(14, 10))
    fig.suptitle('WGAN训练损失函数图 - 信用卡欺诈检测数据集', fontsize=16, fontweight='bold')
    
    # 子图1：生成器和判别器损失
    axes[0, 0].plot(epochs, history['critic_loss'], 'b-', label='判别器损失', linewidth=2)
    axes[0, 0].plot(epochs, history['generator_loss'], 'r-', label='生成器损失', linewidth=2)
    axes[0, 0].set_title('生成器与判别器损失对比', fontsize=12, fontweight='bold')
    axes[0, 0].set_xlabel('训练轮次 (Epoch)')
    axes[0, 0].set_ylabel('损失值')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 子图2：判别器对真实和假样本的损失
    if 'critic_loss_real' in history and 'critic_loss_fake' in history:
        axes[0, 1].plot(epochs, history['critic_loss_real'], 'g-', label='真实样本损失', linewidth=2)
        axes[0, 1].plot(epochs, history['critic_loss_fake'], 'orange', label='假样本损失', linewidth=2)
        axes[0, 1].set_title('判别器对真实样本与假样本的损失', fontsize=12, fontweight='bold')
        axes[0, 1].set_xlabel('训练轮次 (Epoch)')
        axes[0, 1].set_ylabel('损失值')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
    
    # 子图3：Wasserstein距离
    if 'wasserstein_distance' in history:
        axes[1, 0].plot(epochs, history['wasserstein_distance'], 'purple', label='Wasserstein距离', linewidth=2)
        axes[1, 0].set_title('Wasserstein距离变化', fontsize=12, fontweight='bold')
        axes[1, 0].set_xlabel('训练轮次 (Epoch)')
        axes[1, 0].set_ylabel('Wasserstein距离')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
    
    # 子图4：损失收敛趋势
    # 计算移动平均
    window = min(20, len(history['critic_loss']) // 5)
    if window > 1:
        critic_ma = np.convolve(history['critic_loss'], np.ones(window)/window, mode='valid')
        generator_ma = np.convolve(history['generator_loss'], np.ones(window)/window, mode='valid')
        ma_epochs = range(window, len(history['critic_loss']) + 1)
        
        axes[1, 1].plot(ma_epochs, critic_ma, 'b-', label=f'判别器损失(MA-{window})', linewidth=2)
        axes[1, 1].plot(ma_epochs, generator_ma, 'r-', label=f'生成器损失(MA-{window})', linewidth=2)
        axes[1, 1].set_title('损失收敛趋势（移动平均）', fontsize=12, fontweight='bold')
        axes[1, 1].set_xlabel('训练轮次 (Epoch)')
        axes[1, 1].set_ylabel('损失值')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"WGAN训练损失函数图已保存到: {save_path}")
    plt.close()


def main():
    """
    主函数
    """
    np.random.seed(42)
    
    print("=== 快速WGAN损失函数可视化 ===")
    
    # 加载数据集
    X, y = load_creditcard_dataset()
    if X is None or y is None:
        print("无法加载数据集，请检查数据集路径")
        return
    
    # 数据预处理
    scaler = StandardScaler()
    X = scaler.fit_transform(X)
    
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # 使用ADASYN生成合成样本
    print("\n正在使用ADASYN生成合成样本...")
    adasyn = ADASYN(random_state=42)
    X_resampled, y_resampled = adasyn.fit_resample(X_train, y_train)
    
    # 获取少数类样本
    minority_class = min(get_class_distribution(y_resampled)[0], key=get_class_distribution(y_resampled)[0].get)
    minority_indices = np.where(y_resampled == minority_class)[0]
    X_minority = X_resampled[minority_indices]
    
    original_minority_count = np.sum(y_train == minority_class)
    adasyn_samples = X_minority[original_minority_count:]
    
    # 初始化WGAN
    wgan = WGAN(
        input_dim=X_resampled.shape[1],
        latent_dim=30,
        clip_value=0.01,
        n_critic=3,
        learning_rate=0.0001
    )
    
    # 训练WGAN
    print("\n正在训练WGAN...")
    start_time = time.time()
    
    history = wgan.train_with_adasyn_samples(
        X_minority[:original_minority_count],
        adasyn_samples,
        batch_size=32,
        epochs=200,  # 适中的训练轮数
        verbose=1
    )
    
    end_time = time.time()
    print(f"\nWGAN训练完成，耗时: {end_time - start_time:.2f} 秒")
    
    # 生成损失函数图
    output_dir = "results/quick_wgan_loss"
    plot_simple_loss_curves(history, os.path.join(output_dir, 'wgan_loss_curves.png'))
    
    print(f"\n损失函数图已保存到: {output_dir}/wgan_loss_curves.png")


if __name__ == '__main__':
    main()
