"""
WGAN模型模块
实现Wasserstein GAN生成器和判别器
"""

import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models, optimizers
import matplotlib.pyplot as plt
import os


class WGAN:
    """
    Wasserstein GAN实现

    参数:
        input_dim (int): 输入维度
        latent_dim (int): 潜在空间维度
        clip_value (float): 权重裁剪值
        n_critic (int): 每次生成器更新前判别器训练的次数
        learning_rate (float): 学习率
    """

    def __init__(self, input_dim, latent_dim=100, clip_value=0.01, n_critic=5, learning_rate=0.00005):
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        self.clip_value = clip_value
        self.n_critic = n_critic
        self.learning_rate = learning_rate

        # 构建生成器和判别器
        self.generator = self._build_generator()
        self.critic = self._build_critic()

        # 构建WGAN模型
        self._build_wgan()

    def _build_generator(self):
        """
        构建生成器网络

        返回:
            generator (tf.keras.Model): 生成器模型
        """
        # 使用4层全连接层，并对输入层和输出层进行批量归一化
        # 使用ReLU作为隐藏层的激活函数，Tanh作为输出层的激活函数
        model = models.Sequential([
            # 输入层
            layers.Dense(256, input_dim=self.latent_dim),
            layers.BatchNormalization(),  # 输入层批量归一化
            layers.ReLU(),  # 使用ReLU激活函数

            # 第一个隐藏层
            layers.Dense(512),
            layers.BatchNormalization(),
            layers.ReLU(),

            # 第二个隐藏层
            layers.Dense(1024),
            layers.BatchNormalization(),
            layers.ReLU(),

            # 第三个隐藏层
            layers.Dense(512),
            layers.BatchNormalization(),
            layers.ReLU(),

            # 输出层
            layers.Dense(self.input_dim),
            layers.BatchNormalization(),  # 输出层批量归一化
            layers.Activation('tanh')  # 使用Tanh激活函数
        ])

        return model

    def _build_critic(self):
        """
        构建判别器（评论家）网络

        返回:
            critic (tf.keras.Model): 判别器模型
        """
        model = models.Sequential([
            layers.Dense(1024, input_dim=self.input_dim),
            layers.LeakyReLU(alpha=0.2),

            layers.Dense(512),
            layers.LeakyReLU(alpha=0.2),

            layers.Dense(256),
            layers.LeakyReLU(alpha=0.2),

            layers.Dense(128),
            layers.LeakyReLU(alpha=0.2),

            layers.Dense(1)  # 没有激活函数，直接输出实数值
        ])

        return model

    def _build_wgan(self):
        """
        构建WGAN模型
        """
        # 冻结判别器权重
        self.critic.trainable = False

        # 生成器输入
        z = layers.Input(shape=(self.latent_dim,))
        # 生成器生成的样本
        fake_sample = self.generator(z)
        # 判别器对生成样本的评分
        fake_score = self.critic(fake_sample)

        # 构建生成器训练模型
        self.generator_model = models.Model(z, fake_score)
        self.generator_model.compile(
            loss=self._wasserstein_loss,
            optimizer=optimizers.RMSprop(learning_rate=self.learning_rate)
        )

        # 解冻判别器权重
        self.critic.trainable = True

        # 编译判别器
        self.critic.compile(
            loss=self._wasserstein_loss,
            optimizer=optimizers.RMSprop(learning_rate=self.learning_rate)
        )

    def _wasserstein_loss(self, y_true, y_pred):
        """
        Wasserstein损失函数

        参数:
            y_true (tf.Tensor): 真实标签
            y_pred (tf.Tensor): 预测值

        返回:
            loss (tf.Tensor): Wasserstein损失
        """
        return tf.reduce_mean(y_true * y_pred)

    def train(self, X_real, batch_size=64, epochs=1000, verbose=1):
        """
        训练WGAN模型

        参数:
            X_real (np.ndarray): 真实样本
            batch_size (int): 批量大小
            epochs (int): 训练轮数
            verbose (int): 显示详细程度

        返回:
            history (dict): 训练历史
        """
        # 准备标签
        valid = -np.ones((batch_size, 1))  # 真实样本的标签为-1
        fake = np.ones((batch_size, 1))    # 生成样本的标签为1

        history = {
            'critic_loss': [],
            'generator_loss': [],
            'critic_loss_real': [],
            'critic_loss_fake': [],
            'wasserstein_distance': []
        }

        for epoch in range(epochs):
            # 训练判别器
            critic_loss = 0
            critic_loss_real_sum = 0
            critic_loss_fake_sum = 0

            for _ in range(self.n_critic):
                # 随机选择真实样本
                idx = np.random.randint(0, X_real.shape[0], batch_size)
                real_samples = X_real[idx]

                # 生成随机噪声
                noise = np.random.normal(0, 1, (batch_size, self.latent_dim))
                # 生成假样本
                fake_samples = self.generator.predict(noise)

                # 训练判别器
                d_loss_real = self.critic.train_on_batch(real_samples, valid)
                d_loss_fake = self.critic.train_on_batch(fake_samples, fake)
                d_loss = 0.5 * (d_loss_real + d_loss_fake)

                # 裁剪判别器权重
                for layer in self.critic.layers:
                    weights = layer.get_weights()
                    weights = [np.clip(w, -self.clip_value, self.clip_value) for w in weights]
                    layer.set_weights(weights)

                critic_loss += d_loss
                critic_loss_real_sum += d_loss_real
                critic_loss_fake_sum += d_loss_fake

            critic_loss /= self.n_critic
            critic_loss_real_avg = critic_loss_real_sum / self.n_critic
            critic_loss_fake_avg = critic_loss_fake_sum / self.n_critic

            # 训练生成器
            noise = np.random.normal(0, 1, (batch_size, self.latent_dim))
            g_loss = self.generator_model.train_on_batch(noise, valid)

            # 计算Wasserstein距离（近似）
            wasserstein_dist = abs(critic_loss_real_avg - critic_loss_fake_avg)

            # 记录损失
            history['critic_loss'].append(critic_loss)
            history['generator_loss'].append(g_loss)
            history['critic_loss_real'].append(critic_loss_real_avg)
            history['critic_loss_fake'].append(critic_loss_fake_avg)
            history['wasserstein_distance'].append(wasserstein_dist)

            # 打印进度
            if verbose and epoch % 100 == 0:
                print(f"Epoch {epoch}/{epochs}, [Critic loss: {critic_loss:.4f}] [Generator loss: {g_loss:.4f}] [Wasserstein distance: {wasserstein_dist:.4f}]")

        return history

    def generate_samples(self, n_samples):
        """
        生成合成样本

        参数:
            n_samples (int): 要生成的样本数量

        返回:
            synthetic_samples (np.ndarray): 生成的合成样本
        """
        # 生成随机噪声
        noise = np.random.normal(0, 1, (n_samples, self.latent_dim))
        # 生成样本
        synthetic_samples = self.generator.predict(noise)

        return synthetic_samples

    def generate_samples_from_adasyn(self, adasyn_samples):
        """
        使用ADASYN生成的样本作为输入，生成高质量样本

        参数:
            adasyn_samples (np.ndarray): ADASYN生成的样本

        返回:
            synthetic_samples (np.ndarray): WGAN生成的高质量样本
        """
        # 将ADASYN样本映射到潜在空间
        latent_vectors = self._map_to_latent_space(adasyn_samples)
        # 使用映射后的潜在向量生成样本
        synthetic_samples = self.generator.predict(latent_vectors)

        return synthetic_samples

    def train_with_adasyn_samples(self, X_real, adasyn_samples, batch_size=64, epochs=1000, verbose=1):
        """
        使用ADASYN生成的样本作为生成器输入进行训练

        参数:
            X_real (np.ndarray): 真实样本
            adasyn_samples (np.ndarray): ADASYN生成的样本
            batch_size (int): 批量大小
            epochs (int): 训练轮数
            verbose (int): 显示详细程度

        返回:
            history (dict): 训练历史
        """
        # 将ADASYN样本映射到潜在空间
        adasyn_latent_vectors = self._map_to_latent_space(adasyn_samples)

        # 准备标签
        valid = -np.ones((batch_size, 1))  # 真实样本的标签为-1
        fake = np.ones((batch_size, 1))    # 生成样本的标签为1

        history = {
            'critic_loss': [],
            'generator_loss': [],
            'critic_loss_real': [],
            'critic_loss_fake': [],
            'wasserstein_distance': []
        }

        for epoch in range(epochs):
            # 训练判别器
            critic_loss = 0
            critic_loss_real_sum = 0
            critic_loss_fake_sum = 0

            for _ in range(self.n_critic):
                # 随机选择真实样本
                idx = np.random.randint(0, X_real.shape[0], batch_size)
                real_samples = X_real[idx]

                # 使用ADASYN潜在向量生成假样本
                if len(adasyn_latent_vectors) >= batch_size:
                    # 如果ADASYN样本足够多，随机选择
                    latent_idx = np.random.randint(0, len(adasyn_latent_vectors), batch_size)
                    latent_batch = adasyn_latent_vectors[latent_idx]
                else:
                    # 如果ADASYN样本不足，采用随机噪声补充
                    n_noise = batch_size - len(adasyn_latent_vectors)
                    noise = np.random.normal(0, 1, (n_noise, self.latent_dim))
                    # 归一化噪声
                    noise = noise / np.linalg.norm(noise, axis=1, keepdims=True)
                    # 合并ADASYN潜在向量和噪声
                    latent_batch = np.vstack([adasyn_latent_vectors, noise])
                    # 打乱顺序
                    np.random.shuffle(latent_batch)
                    latent_batch = latent_batch[:batch_size]

                # 生成假样本
                fake_samples = self.generator.predict(latent_batch)

                # 训练判别器
                d_loss_real = self.critic.train_on_batch(real_samples, valid)
                d_loss_fake = self.critic.train_on_batch(fake_samples, fake)
                d_loss = 0.5 * (d_loss_real + d_loss_fake)

                # 裁剪判别器权重
                for layer in self.critic.layers:
                    weights = layer.get_weights()
                    weights = [np.clip(w, -self.clip_value, self.clip_value) for w in weights]
                    layer.set_weights(weights)

                critic_loss += d_loss
                critic_loss_real_sum += d_loss_real
                critic_loss_fake_sum += d_loss_fake

            critic_loss /= self.n_critic
            critic_loss_real_avg = critic_loss_real_sum / self.n_critic
            critic_loss_fake_avg = critic_loss_fake_sum / self.n_critic

            # 训练生成器
            # 使用ADASYN潜在向量训练生成器
            if len(adasyn_latent_vectors) >= batch_size:
                latent_idx = np.random.randint(0, len(adasyn_latent_vectors), batch_size)
                latent_batch = adasyn_latent_vectors[latent_idx]
            else:
                # 如果ADASYN样本不足，采用随机噪声补充
                n_noise = batch_size - len(adasyn_latent_vectors)
                noise = np.random.normal(0, 1, (n_noise, self.latent_dim))
                noise = noise / np.linalg.norm(noise, axis=1, keepdims=True)
                latent_batch = np.vstack([adasyn_latent_vectors, noise])
                np.random.shuffle(latent_batch)
                latent_batch = latent_batch[:batch_size]

            g_loss = self.generator_model.train_on_batch(latent_batch, valid)

            # 计算Wasserstein距离（近似）
            wasserstein_dist = abs(critic_loss_real_avg - critic_loss_fake_avg)

            # 记录损失
            history['critic_loss'].append(critic_loss)
            history['generator_loss'].append(g_loss)
            history['critic_loss_real'].append(critic_loss_real_avg)
            history['critic_loss_fake'].append(critic_loss_fake_avg)
            history['wasserstein_distance'].append(wasserstein_dist)

            # 打印进度
            if verbose and epoch % 100 == 0:
                print(f"Epoch {epoch}/{epochs}, [Critic loss: {critic_loss:.4f}] [Generator loss: {g_loss:.4f}] [Wasserstein distance: {wasserstein_dist:.4f}]")

                # 检查是否达到纳什平衡
                if abs(critic_loss) < 0.01 and abs(g_loss) < 0.01:
                    print("\n达到纳什平衡，生成器生成的数据足以“以假乱真”\n")
                    if epoch > epochs // 2:  # 至少训练了一半的轮数
                        print(f"\n提前结束训练，完成了 {epoch}/{epochs} 轮\n")
                        break

        return history

    def _map_to_latent_space(self, samples):
        """
        将样本映射到潜在空间

        参数:
            samples (np.ndarray): 输入样本

        返回:
            latent_vectors (np.ndarray): 映射后的潜在向量
        """
        # 使用自编码器结构进行映射
        encoder = models.Sequential([
            layers.Dense(512, input_shape=(samples.shape[1],)),
            layers.BatchNormalization(),
            layers.ReLU(),

            layers.Dense(256),
            layers.BatchNormalization(),
            layers.ReLU(),

            layers.Dense(self.latent_dim),
            layers.BatchNormalization()
        ])

        # 将样本映射到潜在空间
        latent_vectors = encoder.predict(samples)

        # 归一化
        latent_vectors = latent_vectors / np.linalg.norm(latent_vectors, axis=1, keepdims=True)

        return latent_vectors

    def plot_training_history(self, history, save_path=None, title_prefix="WGAN"):
        """
        绘制训练损失函数图

        参数:
            history (dict): 训练历史
            save_path (str): 保存路径，如果为None则显示图表
            title_prefix (str): 图表标题前缀
        """
        epochs = range(1, len(history['critic_loss']) + 1)

        # 创建2x2的子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'{title_prefix} 训练损失函数图', fontsize=16, fontweight='bold')

        # 子图1：生成器和判别器损失对比
        axes[0, 0].plot(epochs, history['critic_loss'], 'b-', label='判别器损失', linewidth=2)
        axes[0, 0].plot(epochs, history['generator_loss'], 'r-', label='生成器损失', linewidth=2)
        axes[0, 0].set_title('生成器与判别器损失对比', fontsize=14, fontweight='bold')
        axes[0, 0].set_xlabel('训练轮次 (Epoch)')
        axes[0, 0].set_ylabel('损失值 (Loss)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 子图2：判别器对真实样本和假样本的损失
        if 'critic_loss_real' in history and 'critic_loss_fake' in history:
            axes[0, 1].plot(epochs, history['critic_loss_real'], 'g-', label='真实样本损失', linewidth=2)
            axes[0, 1].plot(epochs, history['critic_loss_fake'], 'orange', label='假样本损失', linewidth=2)
            axes[0, 1].set_title('判别器对真实样本与假样本的损失', fontsize=14, fontweight='bold')
            axes[0, 1].set_xlabel('训练轮次 (Epoch)')
            axes[0, 1].set_ylabel('损失值 (Loss)')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)

        # 子图3：Wasserstein距离
        if 'wasserstein_distance' in history:
            axes[1, 0].plot(epochs, history['wasserstein_distance'], 'purple', label='Wasserstein距离', linewidth=2)
            axes[1, 0].set_title('Wasserstein距离变化', fontsize=14, fontweight='bold')
            axes[1, 0].set_xlabel('训练轮次 (Epoch)')
            axes[1, 0].set_ylabel('Wasserstein距离')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

        # 子图4：损失收敛性分析
        # 计算损失的移动平均
        window_size = min(50, len(history['critic_loss']) // 10)
        if window_size > 1:
            critic_ma = np.convolve(history['critic_loss'], np.ones(window_size)/window_size, mode='valid')
            generator_ma = np.convolve(history['generator_loss'], np.ones(window_size)/window_size, mode='valid')
            ma_epochs = range(window_size, len(history['critic_loss']) + 1)

            axes[1, 1].plot(ma_epochs, critic_ma, 'b-', label=f'判别器损失(MA-{window_size})', linewidth=2)
            axes[1, 1].plot(ma_epochs, generator_ma, 'r-', label=f'生成器损失(MA-{window_size})', linewidth=2)
            axes[1, 1].set_title('损失收敛性分析（移动平均）', fontsize=14, fontweight='bold')
            axes[1, 1].set_xlabel('训练轮次 (Epoch)')
            axes[1, 1].set_ylabel('损失值 (Loss)')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            # 确保目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"训练损失函数图已保存到: {save_path}")
            plt.close()
        else:
            plt.show()

    def analyze_training_convergence(self, history):
        """
        分析训练收敛性

        参数:
            history (dict): 训练历史

        返回:
            analysis (dict): 收敛性分析结果
        """
        analysis = {}

        # 计算最终损失
        analysis['final_critic_loss'] = history['critic_loss'][-1]
        analysis['final_generator_loss'] = history['generator_loss'][-1]

        # 计算损失稳定性（最后10%轮次的标准差）
        last_10_percent = max(1, len(history['critic_loss']) // 10)
        analysis['critic_loss_stability'] = np.std(history['critic_loss'][-last_10_percent:])
        analysis['generator_loss_stability'] = np.std(history['generator_loss'][-last_10_percent:])

        # 检查是否收敛
        analysis['is_converged'] = (
            analysis['critic_loss_stability'] < 0.01 and
            analysis['generator_loss_stability'] < 0.01
        )

        # 计算训练效率（损失下降速度）
        if len(history['critic_loss']) > 1:
            critic_improvement = history['critic_loss'][0] - history['critic_loss'][-1]
            generator_improvement = history['generator_loss'][0] - history['generator_loss'][-1]
            analysis['critic_improvement'] = critic_improvement
            analysis['generator_improvement'] = generator_improvement

        # 检查纳什平衡
        final_critic = abs(analysis['final_critic_loss'])
        final_generator = abs(analysis['final_generator_loss'])
        analysis['nash_equilibrium'] = (final_critic < 0.05 and final_generator < 0.05)

        return analysis
