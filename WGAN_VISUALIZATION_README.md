# WGAN训练损失函数可视化指南

本文档介绍如何使用修改后的WGAN模块生成详细的训练损失函数图。

## 主要修改内容

### 1. WGAN模型架构改进 (wgan.py)

- **生成器结构**：修改为4层全连接层
  - 输入层：256个神经元，带批量归一化和ReLU激活
  - 隐藏层1：512个神经元，带批量归一化和ReLU激活
  - 隐藏层2：1024个神经元，带批量归一化和ReLU激活
  - 隐藏层3：512个神经元，带批量归一化和ReLU激活
  - 输出层：输出维度等于输入特征维度，带批量归一化和Tanh激活

- **ADASYN样本输入**：
  - 使用ADASYN生成的少数类样本作为生成器的输入
  - 通过自编码器结构将ADASYN样本映射到潜在空间
  - 在训练过程中优先使用ADASYN潜在向量

- **详细损失记录**：
  - 记录判别器对真实样本和假样本的分别损失
  - 计算Wasserstein距离的近似值
  - 添加训练收敛性分析功能

### 2. 可视化功能增强

- **综合损失分析图**：包含6个子图的详细分析
- **收敛性分析**：自动检测是否达到纳什平衡
- **样本分布对比**：可视化原始样本、ADASYN样本和WGAN样本的分布

## 使用方法

### 方法1：完整可视化分析

运行完整的WGAN训练和可视化分析：

```bash
python visualize_wgan_training.py
```

或使用批处理脚本：

```bash
run_wgan_visualization.bat
```

**输出文件**：
- `results/wgan_visualization/comprehensive_wgan_loss_analysis.png`：综合损失分析图
- `results/wgan_visualization/wgan_training_loss.png`：WGAN训练损失图
- `results/wgan_visualization/samples_distribution_comparison.png`：样本分布对比图
- `results/wgan_visualization/convergence_analysis.txt`：收敛性分析报告

### 方法2：快速损失函数图

快速生成损失函数图（训练轮数较少）：

```bash
python quick_wgan_loss_plot.py
```

或使用批处理脚本：

```bash
run_quick_wgan_loss.bat
```

**输出文件**：
- `results/quick_wgan_loss/wgan_loss_curves.png`：快速损失曲线图

### 方法3：在主框架中使用

在运行主框架时，损失函数图会自动生成：

```bash
python main.py --dataset creditcard --mode full --visualize
```

**输出文件**：
- `results/creditcard/wgan_detailed_training_loss.png`：详细训练损失图

## 损失函数图说明

### 1. 生成器与判别器损失对比
- **蓝线**：判别器损失 (Critic Loss)
- **红线**：生成器损失 (Generator Loss)
- **理想状态**：两条线趋于稳定且接近

### 2. 判别器对真实样本与假样本的损失
- **绿线**：判别器对真实样本的损失
- **橙线**：判别器对假样本的损失
- **理想状态**：两条线逐渐接近，表示生成样本质量提高

### 3. Wasserstein距离变化
- **紫线**：Wasserstein距离的近似值
- **理想状态**：距离逐渐减小并趋于稳定

### 4. 损失收敛性分析（移动平均）
- 使用移动平均平滑损失曲线，更清楚地观察收敛趋势
- **理想状态**：曲线平滑下降并趋于稳定

### 5. 生成器与判别器损失差值
- 显示生成器损失与判别器损失的差值
- **理想状态**：差值趋于0，表示达到纳什平衡

### 6. 训练稳定性分析
- 使用滑动窗口计算损失的标准差
- **理想状态**：标准差逐渐减小，表示训练越来越稳定

## 收敛性判断标准

### 纳什平衡判断
- 判别器损失和生成器损失的绝对值都小于0.05
- 损失在最后10%的训练轮次中保持稳定（标准差小于0.01）

### 训练质量评估
- **收敛**：损失稳定性指标小于0.01
- **纳什平衡**：最终损失绝对值小于0.05
- **训练效率**：损失改进幅度大于0

## 参数调整建议

如果训练不稳定或收敛效果不好，可以尝试调整以下参数：

1. **学习率**：降低学习率（如从0.0001降到0.00005）
2. **批量大小**：增加批量大小以稳定训练
3. **判别器训练次数**：增加n_critic值
4. **权重裁剪值**：调整clip_value（通常在0.01-0.1之间）
5. **潜在空间维度**：调整latent_dim（通常在30-100之间）

## 注意事项

1. 确保creditcard.csv文件位于data目录下
2. 训练过程可能需要较长时间，请耐心等待
3. 如果内存不足，可以减少数据集大小或批量大小
4. 生成的图表为高分辨率PNG格式，适合用于论文或报告
